# Lonors RAG Platform - Production Environment Configuration

# Environment
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=WARNING

# Database Configuration
POSTGRES_DB=lonors_production
POSTGRES_USER=lonors_prod
POSTGRES_PASSWORD=CHANGE_ME_PRODUCTION_DB_PASSWORD
DATABASE_URL=***********************************************************************/lonors_production

# Redis Configuration
REDIS_PASSWORD=CHANGE_ME_PRODUCTION_REDIS_PASSWORD
REDIS_URL=redis://:CHANGE_ME_PRODUCTION_REDIS_PASSWORD@redis:6379/0

# Qdrant Configuration
QDRANT_HOST=qdrant
QDRANT_PORT=6333
QDRANT_COLLECTION_NAME=documents_production

# Security
SECRET_KEY=CHANGE_ME_PRODUCTION_SECRET_KEY_RANDOM_STRING
JWT_SECRET_KEY=CHANGE_ME_PRODUCTION_JWT_SECRET_RANDOM_STRING
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
ALLOWED_ORIGINS=https://lonors.com,https://www.lonors.com,https://app.lonors.com
ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
ALLOWED_HEADERS=*

# File Upload Configuration
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=104857600  # 100MB
ALLOWED_FILE_TYPES=pdf,docx,txt,md

# AI/ML Configuration
DEFAULT_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EMBEDDING_BATCH_SIZE=32
EMBEDDING_DIMENSION=384

# Frontend Configuration
NEXT_PUBLIC_API_URL=https://api.lonors.com
NEXT_PUBLIC_APP_URL=https://app.lonors.com
NEXT_PUBLIC_ENVIRONMENT=production
NEXT_TELEMETRY_DISABLED=1

# Monitoring Configuration
SENTRY_DSN=https://<EMAIL>/project-id
GRAFANA_PASSWORD=CHANGE_ME_PRODUCTION_GRAFANA_PASSWORD

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=CHANGE_ME_SMTP_PASSWORD
FROM_EMAIL=<EMAIL>

# External Services
OPENAI_API_KEY=CHANGE_ME_OPENAI_API_KEY
ANTHROPIC_API_KEY=CHANGE_ME_ANTHROPIC_API_KEY

# Rate Limiting
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=60  # seconds

# Session Configuration
SESSION_TIMEOUT=7200  # 2 hours

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# SSL Configuration
SSL_ENABLED=true
SSL_CERT_PATH=/etc/ssl/certs/lonors.com.crt
SSL_KEY_PATH=/etc/ssl/private/lonors.com.key

# Performance Configuration
WORKER_PROCESSES=4
WORKER_CONNECTIONS=2000
KEEPALIVE_TIMEOUT=65

# Cache Configuration
CACHE_TTL=7200  # 2 hours
CACHE_MAX_SIZE=10000

# Document Processing
MAX_CHUNK_SIZE=1000
CHUNK_OVERLAP=200
PROCESSING_TIMEOUT=600  # 10 minutes

# Search Configuration
DEFAULT_SEARCH_LIMIT=10
MAX_SEARCH_LIMIT=100
DEFAULT_SIMILARITY_THRESHOLD=0.7

# Chat Configuration
DEFAULT_CONTEXT_LIMIT=5
MAX_CONTEXT_LIMIT=10
CHAT_TIMEOUT=30  # seconds

# Database Connection Pool
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Redis Connection Pool
REDIS_POOL_SIZE=20
REDIS_MAX_CONNECTIONS=100

# Logging Configuration
LOG_FORMAT=json
LOG_FILE=/var/log/lonors/app.log
LOG_ROTATION=daily
LOG_RETENTION=30

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# Metrics Configuration
METRICS_ENABLED=true
METRICS_PORT=9090
METRICS_PATH=/metrics

# Security Headers
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000
CSP_ENABLED=true

# Content Delivery
CDN_ENABLED=true
CDN_URL=https://cdn.lonors.com
STATIC_FILES_CDN=true

# Auto-scaling Configuration
AUTO_SCALING_ENABLED=true
MIN_REPLICAS=2
MAX_REPLICAS=10
CPU_THRESHOLD=70
MEMORY_THRESHOLD=80
