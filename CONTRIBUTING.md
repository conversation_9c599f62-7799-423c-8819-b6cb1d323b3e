# Contributing to Lonors AI Agent Platform

Thank you for your interest in contributing to the Lonors AI Agent Platform! This document provides guidelines and information for contributors.

## 🎯 Code of Conduct

We are committed to providing a welcoming and inclusive environment for all contributors. Please be respectful and professional in all interactions.

## 🚀 Getting Started

### Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose
- Python 3.11+
- Node.js 18+
- Git

### Setup Development Environment

1. **Fork and clone the repository**:
   ```bash
   git clone https://github.com/your-username/lonors.git
   cd lonors
   ```

2. **Run the setup script**:
   ```bash
   chmod +x scripts/*.sh
   ./scripts/setup.sh
   ```

3. **Start development environment**:
   ```bash
   ./scripts/dev.sh
   ```

## 📝 Development Workflow

### 1. Create a Feature Branch

```bash
git checkout -b feature/your-feature-name
```

Use descriptive branch names:
- `feature/add-voice-transcription`
- `fix/authentication-bug`
- `docs/update-api-documentation`

### 2. Make Your Changes

Follow our coding standards and best practices:

#### Backend (Python)
- Follow PEP 8 style guidelines
- Use type hints for all functions
- Write docstrings for all public functions
- Add comprehensive tests

#### Frontend (TypeScript)
- Use TypeScript for all new code
- Follow React best practices
- Use TailwindCSS for styling
- Write unit tests for components

### 3. Test Your Changes

```bash
# Run all tests
./scripts/test.sh

# Run specific test suites
./scripts/test.sh --backend-only
./scripts/test.sh --frontend-only
```

### 4. Commit Your Changes

We use [Conventional Commits](https://www.conventionalcommits.org/) for commit messages:

```bash
git commit -m "feat: add voice transcription service"
git commit -m "fix: resolve authentication token expiration"
git commit -m "docs: update API documentation"
```

**Commit Types:**
- `feat:` New features
- `fix:` Bug fixes
- `docs:` Documentation changes
- `style:` Code style changes (formatting, etc.)
- `refactor:` Code refactoring
- `test:` Adding or updating tests
- `chore:` Maintenance tasks

### 5. Push and Create Pull Request

```bash
git push origin feature/your-feature-name
```

Create a pull request with:
- Clear title and description
- Reference to related issues
- Screenshots for UI changes
- Test coverage information

## 🧪 Testing Guidelines

### Test Requirements

- **Minimum 90% test coverage** for new code
- **Unit tests** for all business logic
- **Integration tests** for API endpoints
- **End-to-end tests** for critical user flows

### Writing Tests

#### Backend Tests (pytest)

```python
import pytest
from httpx import AsyncClient

@pytest.mark.asyncio
async def test_create_user(async_client: AsyncClient):
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "SecurePass123!"
    }
    
    response = await async_client.post("/api/v1/auth/register", json=user_data)
    
    assert response.status_code == 201
    assert response.json()["email"] == user_data["email"]
```

#### Frontend Tests (Jest + React Testing Library)

```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { LoginForm } from './LoginForm'

test('submits login form with valid data', async () => {
  render(<LoginForm />)
  
  fireEvent.change(screen.getByLabelText(/email/i), {
    target: { value: '<EMAIL>' }
  })
  fireEvent.change(screen.getByLabelText(/password/i), {
    target: { value: 'password123' }
  })
  
  fireEvent.click(screen.getByRole('button', { name: /login/i }))
  
  expect(await screen.findByText(/welcome/i)).toBeInTheDocument()
})
```

## 📋 Code Style Guidelines

### Python (Backend)

```python
from typing import List, Optional
import structlog

logger = structlog.get_logger(__name__)

class UserService:
    """Service for user management operations."""
    
    async def create_user(
        self, 
        email: str, 
        username: str, 
        password: str
    ) -> User:
        """
        Create a new user.
        
        Args:
            email: User email address
            username: Unique username
            password: Plain text password
            
        Returns:
            User: Created user instance
            
        Raises:
            ConflictError: If email or username already exists
        """
        logger.info("Creating user", email=email, username=username)
        
        # Implementation here
        
        return user
```

### TypeScript (Frontend)

```typescript
interface User {
  id: number
  email: string
  username: string
  fullName?: string
}

interface LoginFormProps {
  onSubmit: (credentials: LoginCredentials) => Promise<void>
  isLoading?: boolean
}

export const LoginForm: React.FC<LoginFormProps> = ({ 
  onSubmit, 
  isLoading = false 
}) => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    await onSubmit({ email, password })
  }
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Form implementation */}
    </form>
  )
}
```

## 🔒 Security Guidelines

### Authentication & Authorization

- Always validate user permissions
- Use JWT tokens for authentication
- Implement proper RBAC (Role-Based Access Control)
- Never expose sensitive data in API responses

### Input Validation

- Validate all user inputs
- Use Pydantic schemas for API validation
- Sanitize data before database operations
- Implement rate limiting for API endpoints

### Security Best Practices

- Use HTTPS in production
- Implement proper CORS policies
- Add security headers
- Regular dependency updates
- Security scanning in CI/CD

## 📚 Documentation

### API Documentation

- Use OpenAPI/Swagger specifications
- Document all endpoints with examples
- Include error response formats
- Provide authentication requirements

### Code Documentation

- Write clear docstrings for all functions
- Include type hints in Python code
- Document complex business logic
- Update README files when needed

## 🐛 Bug Reports

When reporting bugs, please include:

1. **Clear description** of the issue
2. **Steps to reproduce** the problem
3. **Expected behavior** vs actual behavior
4. **Environment details** (OS, browser, versions)
5. **Screenshots or logs** if applicable

Use our bug report template:

```markdown
## Bug Description
Brief description of the bug

## Steps to Reproduce
1. Go to...
2. Click on...
3. See error

## Expected Behavior
What should happen

## Actual Behavior
What actually happens

## Environment
- OS: [e.g., macOS 12.0]
- Browser: [e.g., Chrome 95.0]
- Version: [e.g., v1.0.0]

## Additional Context
Any other relevant information
```

## 💡 Feature Requests

For feature requests, please:

1. **Check existing issues** to avoid duplicates
2. **Describe the problem** you're trying to solve
3. **Propose a solution** with implementation details
4. **Consider alternatives** and their trade-offs
5. **Provide use cases** and examples

## 🔄 Pull Request Process

### Before Submitting

- [ ] Code follows style guidelines
- [ ] Tests pass locally
- [ ] Documentation is updated
- [ ] Commit messages follow conventions
- [ ] No merge conflicts

### PR Requirements

- [ ] Clear title and description
- [ ] Links to related issues
- [ ] Test coverage maintained/improved
- [ ] Breaking changes documented
- [ ] Screenshots for UI changes

### Review Process

1. **Automated checks** must pass
2. **Code review** by maintainers
3. **Testing** in staging environment
4. **Approval** from code owners
5. **Merge** after all requirements met

## 🏷️ Issue Labels

We use labels to categorize issues:

- `bug` - Something isn't working
- `enhancement` - New feature or request
- `documentation` - Improvements to documentation
- `good first issue` - Good for newcomers
- `help wanted` - Extra attention needed
- `priority: high` - High priority issues
- `status: in progress` - Currently being worked on

## 🎉 Recognition

Contributors will be recognized in:

- Release notes
- Contributors section in README
- Special mentions for significant contributions

## 📞 Getting Help

If you need help:

1. Check the [documentation](docs/)
2. Search existing [issues](https://github.com/lonors/lonors/issues)
3. Join our [Discord community](https://discord.gg/lonors)
4. Create a new issue with the `help wanted` label

## 📄 License

By contributing to Lonors, you agree that your contributions will be licensed under the MIT License.

Thank you for contributing to Lonors AI Agent Platform! 🚀
