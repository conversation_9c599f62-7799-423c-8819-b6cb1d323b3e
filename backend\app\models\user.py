"""User-related database models."""

import enum
from datetime import datetime
from typing import Optional

from sqlalchemy import <PERSON>olean, DateT<PERSON>, Enum, ForeignKey, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base


class UserRoleEnum(str, enum.Enum):
    """User role enumeration."""
    USER = "user"
    MODERATOR = "moderator"
    ADMIN = "admin"
    SUPERUSER = "superuser"


class User(Base):
    """User model for authentication and authorization."""
    
    __tablename__ = "users"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Authentication fields
    email: Mapped[str] = mapped_column(String(255), unique=True, index=True, nullable=False)
    username: Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False)
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False)
    
    # Profile fields
    full_name: Mapped[Optional[str]] = mapped_column(String(255))
    avatar_url: Mapped[Optional[str]] = mapped_column(String(500))
    bio: Mapped[Optional[str]] = mapped_column(Text)
    
    # Status fields
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_verified: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # Role and permissions
    role: Mapped[UserRoleEnum] = mapped_column(
        Enum(UserRoleEnum), 
        default=UserRoleEnum.USER, 
        nullable=False
    )
    
    # OAuth fields
    google_id: Mapped[Optional[str]] = mapped_column(String(255), unique=True)
    github_id: Mapped[Optional[str]] = mapped_column(String(255), unique=True)
    
    # API access
    api_key: Mapped[Optional[str]] = mapped_column(String(255), unique=True)
    api_key_created_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    last_login_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Password reset
    password_reset_token: Mapped[Optional[str]] = mapped_column(String(255))
    password_reset_expires: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Email verification
    email_verification_token: Mapped[Optional[str]] = mapped_column(String(255))
    email_verification_expires: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Relationships
    sessions: Mapped[list["UserSession"]] = relationship(
        "UserSession", 
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    documents: Mapped[list["Document"]] = relationship(
        "Document", 
        back_populates="owner",
        cascade="all, delete-orphan"
    )
    
    agents: Mapped[list["Agent"]] = relationship(
        "Agent", 
        back_populates="owner",
        cascade="all, delete-orphan"
    )
    
    workflows: Mapped[list["Workflow"]] = relationship(
        "Workflow", 
        back_populates="owner",
        cascade="all, delete-orphan"
    )
    
    password_entries: Mapped[list["PasswordEntry"]] = relationship(
        "PasswordEntry", 
        back_populates="owner",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    @property
    def is_admin(self) -> bool:
        """Check if user has admin privileges."""
        return self.role in (UserRoleEnum.ADMIN, UserRoleEnum.SUPERUSER)
    
    @property
    def permissions(self) -> list[str]:
        """Get user permissions based on role."""
        base_permissions = ["read:own"]
        
        if self.role == UserRoleEnum.USER:
            return base_permissions + [
                "create:document",
                "update:own",
                "delete:own",
            ]
        elif self.role == UserRoleEnum.MODERATOR:
            return base_permissions + [
                "create:document",
                "update:own",
                "delete:own",
                "moderate:content",
                "read:users",
            ]
        elif self.role == UserRoleEnum.ADMIN:
            return base_permissions + [
                "create:*",
                "read:*",
                "update:*",
                "delete:*",
                "manage:users",
                "manage:system",
            ]
        elif self.role == UserRoleEnum.SUPERUSER:
            return ["*"]  # All permissions
        
        return base_permissions


class UserRole(Base):
    """User role model for fine-grained permissions."""
    
    __tablename__ = "user_roles"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    role_name: Mapped[str] = mapped_column(String(50), nullable=False)
    granted_by: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    granted_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Relationships
    user: Mapped["User"] = relationship("User", foreign_keys=[user_id])
    granter: Mapped["User"] = relationship("User", foreign_keys=[granted_by])


class UserSession(Base):
    """User session model for tracking active sessions."""
    
    __tablename__ = "user_sessions"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    
    # Session details
    session_token: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    refresh_token: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    
    # Client information
    user_agent: Mapped[Optional[str]] = mapped_column(Text)
    ip_address: Mapped[Optional[str]] = mapped_column(String(45))  # IPv6 compatible
    device_info: Mapped[Optional[str]] = mapped_column(Text)
    
    # Session status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    last_accessed_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    expires_at: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="sessions")
    
    def __repr__(self) -> str:
        return f"<UserSession(id={self.id}, user_id={self.user_id}, active={self.is_active})>"
    
    @property
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return datetime.utcnow() > self.expires_at
