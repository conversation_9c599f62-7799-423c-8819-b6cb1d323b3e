"""Vector store service for Qdrant integration."""

import uuid
from typing import Dict, List, Optional, Tuple

import structlog
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.exceptions import ResponseHandlingException

from app.core.config import get_settings
from app.core.exceptions import VectorDatabaseError

settings = get_settings()
logger = structlog.get_logger(__name__)


class VectorStore:
    """Service for managing vectors in Qdrant database."""
    
    def __init__(self, collection_name: str = "documents"):
        """
        Initialize vector store.
        
        Args:
            collection_name: Name of the Qdrant collection
        """
        self.collection_name = collection_name
        self.client: Optional[QdrantClient] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize connection to Qdrant."""
        if self._initialized:
            return
        
        try:
            logger.info("Initializing Qdrant client", host=settings.QDRANT_HOST)
            
            self.client = QdrantClient(
                host=settings.QDRANT_HOST,
                port=settings.QDRANT_PORT,
                timeout=30,
            )
            
            # Test connection
            collections = self.client.get_collections()
            logger.info("Qdrant connection established", collections_count=len(collections.collections))
            
            self._initialized = True
            
        except Exception as e:
            logger.error("Failed to initialize Qdrant client", error=str(e))
            raise VectorDatabaseError(f"Failed to connect to Qdrant: {str(e)}")
    
    async def create_collection(
        self, 
        dimension: int, 
        distance_metric: str = "Cosine"
    ) -> None:
        """
        Create a collection in Qdrant.
        
        Args:
            dimension: Vector dimension
            distance_metric: Distance metric (Cosine, Dot, Euclid)
            
        Raises:
            VectorDatabaseError: If collection creation fails
        """
        await self.initialize()
        
        try:
            # Check if collection already exists
            try:
                collection_info = self.client.get_collection(self.collection_name)
                logger.info(
                    "Collection already exists",
                    collection_name=self.collection_name,
                    vectors_count=collection_info.vectors_count,
                )
                return
            except ResponseHandlingException:
                # Collection doesn't exist, create it
                pass
            
            logger.info(
                "Creating Qdrant collection",
                collection_name=self.collection_name,
                dimension=dimension,
                distance_metric=distance_metric,
            )
            
            # Map distance metric
            distance_map = {
                "Cosine": models.Distance.COSINE,
                "Dot": models.Distance.DOT,
                "Euclid": models.Distance.EUCLID,
            }
            
            distance = distance_map.get(distance_metric, models.Distance.COSINE)
            
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=models.VectorParams(
                    size=dimension,
                    distance=distance,
                ),
                optimizers_config=models.OptimizersConfig(
                    default_segment_number=2,
                ),
                hnsw_config=models.HnswConfig(
                    m=16,
                    ef_construct=100,
                    full_scan_threshold=10000,
                ),
            )
            
            logger.info("Collection created successfully", collection_name=self.collection_name)
            
        except Exception as e:
            logger.error("Failed to create collection", error=str(e))
            raise VectorDatabaseError(f"Failed to create collection: {str(e)}")
    
    async def add_vectors(
        self, 
        vectors: List[List[float]], 
        payloads: List[Dict], 
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Add vectors to the collection.
        
        Args:
            vectors: List of vector embeddings
            payloads: List of metadata for each vector
            ids: Optional list of vector IDs
            
        Returns:
            List[str]: List of vector IDs
            
        Raises:
            VectorDatabaseError: If adding vectors fails
        """
        await self.initialize()
        
        if len(vectors) != len(payloads):
            raise VectorDatabaseError("Number of vectors must match number of payloads")
        
        if not vectors:
            return []
        
        try:
            # Generate IDs if not provided
            if ids is None:
                ids = [str(uuid.uuid4()) for _ in vectors]
            
            logger.info(
                "Adding vectors to collection",
                collection_name=self.collection_name,
                count=len(vectors),
            )
            
            # Prepare points
            points = [
                models.PointStruct(
                    id=vector_id,
                    vector=vector,
                    payload=payload,
                )
                for vector_id, vector, payload in zip(ids, vectors, payloads)
            ]
            
            # Upload points
            operation_info = self.client.upsert(
                collection_name=self.collection_name,
                points=points,
            )
            
            logger.info(
                "Vectors added successfully",
                collection_name=self.collection_name,
                operation_id=operation_info.operation_id,
                status=operation_info.status,
            )
            
            return ids
            
        except Exception as e:
            logger.error("Failed to add vectors", error=str(e))
            raise VectorDatabaseError(f"Failed to add vectors: {str(e)}")
    
    async def search_vectors(
        self, 
        query_vector: List[float], 
        limit: int = 10, 
        score_threshold: Optional[float] = None,
        filter_conditions: Optional[Dict] = None
    ) -> List[Tuple[str, float, Dict]]:
        """
        Search for similar vectors.
        
        Args:
            query_vector: Query vector
            limit: Maximum number of results
            score_threshold: Minimum similarity score
            filter_conditions: Optional filter conditions
            
        Returns:
            List[Tuple[str, float, Dict]]: List of (id, score, payload) tuples
            
        Raises:
            VectorDatabaseError: If search fails
        """
        await self.initialize()
        
        try:
            logger.debug(
                "Searching vectors",
                collection_name=self.collection_name,
                limit=limit,
                score_threshold=score_threshold,
            )
            
            # Prepare filter
            query_filter = None
            if filter_conditions:
                query_filter = models.Filter(
                    must=[
                        models.FieldCondition(
                            key=key,
                            match=models.MatchValue(value=value),
                        )
                        for key, value in filter_conditions.items()
                    ]
                )
            
            # Perform search
            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_vector,
                limit=limit,
                score_threshold=score_threshold,
                query_filter=query_filter,
                with_payload=True,
            )
            
            # Format results
            results = [
                (point.id, point.score, point.payload or {})
                for point in search_result
            ]
            
            logger.debug(
                "Vector search completed",
                results_count=len(results),
                collection_name=self.collection_name,
            )
            
            return results
            
        except Exception as e:
            logger.error("Vector search failed", error=str(e))
            raise VectorDatabaseError(f"Vector search failed: {str(e)}")
    
    async def get_vector(self, vector_id: str) -> Optional[Tuple[List[float], Dict]]:
        """
        Get a specific vector by ID.
        
        Args:
            vector_id: Vector ID
            
        Returns:
            Optional[Tuple[List[float], Dict]]: Vector and payload if found
            
        Raises:
            VectorDatabaseError: If retrieval fails
        """
        await self.initialize()
        
        try:
            points = self.client.retrieve(
                collection_name=self.collection_name,
                ids=[vector_id],
                with_vectors=True,
                with_payload=True,
            )
            
            if not points:
                return None
            
            point = points[0]
            return point.vector, point.payload or {}
            
        except Exception as e:
            logger.error("Failed to get vector", error=str(e), vector_id=vector_id)
            raise VectorDatabaseError(f"Failed to get vector: {str(e)}")
    
    async def delete_vectors(self, vector_ids: List[str]) -> None:
        """
        Delete vectors by IDs.
        
        Args:
            vector_ids: List of vector IDs to delete
            
        Raises:
            VectorDatabaseError: If deletion fails
        """
        await self.initialize()
        
        if not vector_ids:
            return
        
        try:
            logger.info(
                "Deleting vectors",
                collection_name=self.collection_name,
                count=len(vector_ids),
            )
            
            operation_info = self.client.delete(
                collection_name=self.collection_name,
                points_selector=models.PointIdsList(
                    points=vector_ids,
                ),
            )
            
            logger.info(
                "Vectors deleted successfully",
                collection_name=self.collection_name,
                operation_id=operation_info.operation_id,
            )
            
        except Exception as e:
            logger.error("Failed to delete vectors", error=str(e))
            raise VectorDatabaseError(f"Failed to delete vectors: {str(e)}")
    
    async def get_collection_info(self) -> Dict:
        """
        Get collection information.
        
        Returns:
            Dict: Collection information
            
        Raises:
            VectorDatabaseError: If retrieval fails
        """
        await self.initialize()
        
        try:
            collection_info = self.client.get_collection(self.collection_name)
            
            return {
                "name": self.collection_name,
                "vectors_count": collection_info.vectors_count,
                "indexed_vectors_count": collection_info.indexed_vectors_count,
                "points_count": collection_info.points_count,
                "segments_count": collection_info.segments_count,
                "status": collection_info.status,
                "optimizer_status": collection_info.optimizer_status,
                "disk_data_size": collection_info.disk_data_size,
                "ram_data_size": collection_info.ram_data_size,
            }
            
        except Exception as e:
            logger.error("Failed to get collection info", error=str(e))
            raise VectorDatabaseError(f"Failed to get collection info: {str(e)}")
    
    async def health_check(self) -> Dict:
        """
        Check Qdrant health.
        
        Returns:
            Dict: Health status
        """
        try:
            await self.initialize()
            collections = self.client.get_collections()
            
            return {
                "status": "healthy",
                "collections_count": len(collections.collections),
                "initialized": self._initialized,
            }
            
        except Exception as e:
            logger.error("Qdrant health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "error": str(e),
                "initialized": self._initialized,
            }


# Global vector store instance
vector_store = VectorStore()
