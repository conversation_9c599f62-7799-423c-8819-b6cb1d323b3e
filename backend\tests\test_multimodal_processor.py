"""Tests for multi-modal document processing service."""

import asyncio
import io
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from PIL import Image

from app.services.multimodal_processor import MultiModalProcessor, multimodal_processor
from app.core.exceptions import ProcessingError


class TestMultiModalProcessor:
    """Test cases for multi-modal document processor."""
    
    @pytest.fixture
    def processor(self):
        """Create processor instance for testing."""
        return MultiModalProcessor()
    
    @pytest.fixture
    def sample_image_bytes(self):
        """Create sample image bytes for testing."""
        # Create a simple test image
        image = Image.new('RGB', (100, 100), color='white')
        
        # Add some text-like patterns
        from PIL import ImageDraw, ImageFont
        draw = ImageDraw.Draw(image)
        
        try:
            # Try to use a default font
            font = ImageFont.load_default()
        except:
            font = None
        
        draw.text((10, 10), "Test Document", fill='black', font=font)
        draw.text((10, 30), "Sample Text", fill='black', font=font)
        
        # Convert to bytes
        img_bytes = io.BytesIO()
        image.save(img_bytes, format='PNG')
        return img_bytes.getvalue()
    
    @pytest.fixture
    def sample_pdf_bytes(self):
        """Create sample PDF bytes for testing."""
        # This would normally be a real PDF file
        # For testing, we'll use a mock
        return b"Mock PDF content"
    
    @pytest.fixture
    def sample_audio_bytes(self):
        """Create sample audio bytes for testing."""
        # This would normally be a real audio file
        # For testing, we'll use a mock
        return b"Mock audio content"
    
    @pytest.mark.asyncio
    async def test_processor_initialization(self, processor):
        """Test processor initialization."""
        # The processor should initialize without errors
        assert processor is not None
        
        # Check if models are initialized (may be None in test environment)
        # This is expected to fail in test environment without proper setup
        try:
            processor._initialize_models()
        except Exception:
            # Expected in test environment
            pass
    
    @pytest.mark.asyncio
    async def test_get_supported_formats(self, processor):
        """Test getting supported file formats."""
        formats = await processor.get_supported_formats()
        
        assert "images" in formats
        assert "audio" in formats
        assert "documents" in formats
        
        # Check specific formats
        assert "png" in formats["images"]
        assert "jpg" in formats["images"]
        assert "wav" in formats["audio"]
        assert "mp3" in formats["audio"]
        assert "pdf" in formats["documents"]
    
    @pytest.mark.asyncio
    @patch('app.services.multimodal_processor.easyocr.Reader')
    async def test_image_text_extraction(self, mock_reader, processor, sample_image_bytes):
        """Test text extraction from images."""
        # Mock OCR reader
        mock_reader_instance = MagicMock()
        mock_reader_instance.readtext.return_value = [
            ([(0, 0), (100, 0), (100, 20), (0, 20)], "Test Document", 0.9),
            ([(0, 25), (80, 25), (80, 45), (0, 45)], "Sample Text", 0.8)
        ]
        mock_reader.return_value = mock_reader_instance
        processor.ocr_reader = mock_reader_instance
        
        result = await processor.process_image_file(sample_image_bytes, "png")
        
        assert "text" in result
        assert "Test Document" in result["text"]
        assert "Sample Text" in result["text"]
        assert result["has_text"] is True
        assert result["format"] == "png"
    
    @pytest.mark.asyncio
    @patch('app.services.multimodal_processor.whisper.load_model')
    @patch('app.services.multimodal_processor.librosa.load')
    async def test_audio_text_extraction(self, mock_librosa, mock_whisper, processor, sample_audio_bytes):
        """Test text extraction from audio files."""
        # Mock Whisper model
        mock_model = MagicMock()
        mock_model.transcribe.return_value = {
            "text": "This is a test audio transcription.",
            "language": "en",
            "segments": [
                {"start": 0.0, "end": 3.0, "text": "This is a test audio transcription."}
            ]
        }
        mock_whisper.return_value = mock_model
        processor.whisper_model = mock_model
        
        # Mock librosa
        mock_librosa.return_value = ([0.1, 0.2, 0.3], 16000)  # (audio_data, sample_rate)
        
        with patch('tempfile.NamedTemporaryFile') as mock_temp:
            mock_temp.return_value.__enter__.return_value.name = "/tmp/test.wav"
            mock_temp.return_value.__enter__.return_value.write = MagicMock()
            mock_temp.return_value.__enter__.return_value.flush = MagicMock()
            
            with patch('app.services.multimodal_processor.MultiModalProcessor._convert_audio_to_wav') as mock_convert:
                mock_convert.return_value = "/tmp/test.wav"
                
                result = await processor.process_audio_file(sample_audio_bytes, "wav")
        
        assert "text" in result
        assert result["text"] == "This is a test audio transcription."
        assert result["language"] == "en"
        assert "duration_seconds" in result
    
    @pytest.mark.asyncio
    @patch('app.services.multimodal_processor.convert_from_bytes')
    async def test_pdf_with_images_processing(self, mock_convert, processor, sample_pdf_bytes):
        """Test PDF processing with image extraction."""
        # Mock PDF to image conversion
        mock_image = MagicMock()
        mock_image.size = (800, 600)
        mock_convert.return_value = [mock_image]
        
        # Mock OCR processing
        with patch.object(processor, '_process_pdf_page') as mock_process_page:
            mock_process_page.return_value = {
                "page_number": 1,
                "text": "Sample PDF text content",
                "images": [],
                "image_regions": []
            }
            
            result = await processor.process_pdf_with_images(sample_pdf_bytes, extract_images=False)
        
        assert "pages" in result
        assert "total_text" in result
        assert "page_count" in result
        assert result["page_count"] == 1
        assert "Sample PDF text content" in result["total_text"]
    
    @pytest.mark.asyncio
    async def test_image_content_analysis(self, processor, sample_image_bytes):
        """Test image content analysis."""
        # Create a test image
        image = Image.open(io.BytesIO(sample_image_bytes))
        
        analysis = await processor._analyze_image_content(image)
        
        assert "dominant_color" in analysis
        assert "edge_density" in analysis
        assert "likely_text_image" in analysis
        assert "brightness" in analysis
        assert "contrast" in analysis
        
        # Check data types
        assert isinstance(analysis["dominant_color"], list)
        assert isinstance(analysis["edge_density"], float)
        assert isinstance(analysis["likely_text_image"], bool)
    
    @pytest.mark.asyncio
    async def test_audio_conversion(self, processor):
        """Test audio format conversion."""
        with patch('app.services.multimodal_processor.AudioSegment.from_file') as mock_from_file:
            mock_audio = MagicMock()
            mock_from_file.return_value = mock_audio
            
            with patch('tempfile.NamedTemporaryFile') as mock_temp:
                mock_temp.return_value.__enter__.return_value.name = "/tmp/test.mp3"
                
                result = await processor._convert_audio_to_wav("/tmp/test.mp3", "mp3")
                
                assert result == "/tmp/test.wav"
                mock_audio.export.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_error_handling_image_processing(self, processor):
        """Test error handling in image processing."""
        # Test with invalid image data
        invalid_image_data = b"invalid image data"
        
        with pytest.raises(ProcessingError):
            await processor.process_image_file(invalid_image_data, "png")
    
    @pytest.mark.asyncio
    async def test_error_handling_audio_processing(self, processor):
        """Test error handling in audio processing."""
        # Test with invalid audio data
        invalid_audio_data = b"invalid audio data"
        
        # Mock the whisper model to avoid initialization issues
        processor.whisper_model = MagicMock()
        
        with pytest.raises(ProcessingError):
            await processor.process_audio_file(invalid_audio_data, "wav")
    
    @pytest.mark.asyncio
    async def test_health_check(self, processor):
        """Test health check functionality."""
        # Mock the components
        processor.ocr_reader = MagicMock()
        processor.whisper_model = MagicMock()
        
        with patch.object(processor, '_extract_text_from_image') as mock_extract:
            mock_extract.return_value = "test text"
            
            health_status = await processor.health_check()
        
        assert "status" in health_status
        assert "components" in health_status
        assert health_status["status"] in ["healthy", "unhealthy"]
    
    @pytest.mark.asyncio
    async def test_confidence_calculation(self, processor):
        """Test confidence calculation for Whisper segments."""
        # Test with segments containing confidence scores
        segments_with_confidence = [
            {"confidence": 0.9},
            {"confidence": 0.8},
            {"confidence": 0.7}
        ]
        
        confidence = processor._calculate_average_confidence(segments_with_confidence)
        assert confidence == 0.8  # Average of 0.9, 0.8, 0.7
        
        # Test with segments without confidence scores
        segments_without_confidence = [
            {"text": "hello"},
            {"text": "world"}
        ]
        
        confidence = processor._calculate_average_confidence(segments_without_confidence)
        assert confidence == 0.8  # Default confidence
        
        # Test with empty segments
        confidence = processor._calculate_average_confidence([])
        assert confidence == 0.0
    
    @pytest.mark.asyncio
    async def test_image_region_detection(self, processor):
        """Test image region detection for PDF processing."""
        import numpy as np
        
        # Create a test image array
        test_image = np.ones((600, 800, 3), dtype=np.uint8) * 255  # White image
        
        # Add a dark rectangle (simulating an image region)
        test_image[100:200, 100:300] = 0  # Black rectangle
        
        with patch('cv2.findContours') as mock_find_contours:
            # Mock contours
            mock_contour = np.array([[[100, 100]], [[300, 100]], [[300, 200]], [[100, 200]]])
            mock_find_contours.return_value = ([mock_contour], None)
            
            regions = await processor._detect_image_regions(test_image)
        
        assert len(regions) > 0
        assert "x" in regions[0]
        assert "y" in regions[0]
        assert "width" in regions[0]
        assert "height" in regions[0]
    
    @pytest.mark.asyncio
    async def test_global_instance(self):
        """Test that global instance is properly initialized."""
        assert multimodal_processor is not None
        assert isinstance(multimodal_processor, MultiModalProcessor)
    
    @pytest.mark.asyncio
    async def test_ocr_fallback(self, processor, sample_image_bytes):
        """Test OCR fallback mechanism."""
        # Mock EasyOCR to fail
        processor.ocr_reader = None
        
        with patch('app.services.multimodal_processor.pytesseract.image_to_string') as mock_tesseract:
            mock_tesseract.return_value = "Tesseract extracted text"
            
            image = Image.open(io.BytesIO(sample_image_bytes))
            result = await processor._extract_text_from_image(image)
            
            assert result == "Tesseract extracted text"
    
    @pytest.mark.asyncio
    async def test_pdf_ocr_fallback(self, processor, sample_pdf_bytes):
        """Test PDF OCR fallback when standard extraction fails."""
        with patch('app.services.multimodal_processor.convert_from_bytes') as mock_convert:
            mock_image = MagicMock()
            mock_convert.return_value = [mock_image]
            
            with patch.object(processor, 'process_pdf_with_images') as mock_process:
                mock_process.return_value = {
                    "total_text": "OCR extracted text from PDF",
                    "page_count": 1
                }
                
                # Simulate poor standard extraction
                with patch('app.services.multimodal_processor.PdfReader') as mock_reader:
                    mock_page = MagicMock()
                    mock_page.extract_text.return_value = ""  # Empty text
                    mock_reader.return_value.pages = [mock_page]
                    
                    # This would be called from document processor
                    # We're testing the logic here
                    result = await processor.process_pdf_with_images(sample_pdf_bytes)
                    
                    assert "total_text" in result
                    assert result["total_text"] == "OCR extracted text from PDF"


@pytest.mark.integration
class TestMultiModalIntegration:
    """Integration tests for multi-modal processing."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_image_processing(self):
        """Test end-to-end image processing workflow."""
        # This test would require actual image files and OCR setup
        # Skipped in unit tests but important for integration testing
        pytest.skip("Requires actual OCR setup and image files")
    
    @pytest.mark.asyncio
    async def test_end_to_end_audio_processing(self):
        """Test end-to-end audio processing workflow."""
        # This test would require actual audio files and Whisper setup
        # Skipped in unit tests but important for integration testing
        pytest.skip("Requires actual Whisper setup and audio files")
    
    @pytest.mark.asyncio
    async def test_performance_large_files(self):
        """Test performance with large files."""
        # This test would check processing time and memory usage
        # Skipped in unit tests but important for performance testing
        pytest.skip("Performance test - requires large test files")
