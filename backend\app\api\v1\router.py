"""Main API router for version 1."""

from fastapi import APIRouter

from app.api.v1.endpoints import (
    auth,
    documents,
    health,
    users,
)
from app.api.v1 import ai_features

# Create main API router
api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(
    health.router,
    prefix="/health",
    tags=["Health"],
)

api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["Authentication"],
)

api_router.include_router(
    users.router,
    prefix="/users",
    tags=["Users"],
)

api_router.include_router(
    documents.router,
    prefix="/documents",
    tags=["Documents"],
)

api_router.include_router(
    ai_features.router,
    prefix="/ai",
    tags=["AI Features"],
)
