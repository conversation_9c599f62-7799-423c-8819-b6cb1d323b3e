'use client'

import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, File, X, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { cn } from '@/lib/utils'

interface UploadFile extends File {
  id: string
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
}

interface DocumentUploadProps {
  onUploadComplete?: (documents: any[]) => void
  className?: string
}

const SUPPORTED_FORMATS = {
  'application/pdf': { ext: '.pdf', icon: '📄', maxSize: 50 },
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { ext: '.docx', icon: '📝', maxSize: 25 },
  'text/plain': { ext: '.txt', icon: '📄', maxSize: 10 },
  'text/markdown': { ext: '.md', icon: '📝', maxSize: 10 },
}

const MAX_FILES = 10
const MAX_TOTAL_SIZE = 100 * 1024 * 1024 // 100MB

export function DocumentUpload({ onUploadComplete, className }: DocumentUploadProps) {
  const [files, setFiles] = useState<UploadFile[]>([])
  const [title, setTitle] = useState('')
  const [tags, setTags] = useState('')
  const [isUploading, setIsUploading] = useState(false)
  const { toast } = useToast()

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    rejectedFiles.forEach(({ file, errors }) => {
      errors.forEach((error: any) => {
        let message = `${file.name}: `
        switch (error.code) {
          case 'file-too-large':
            message += 'File is too large'
            break
          case 'file-invalid-type':
            message += 'File type not supported'
            break
          case 'too-many-files':
            message += 'Too many files selected'
            break
          default:
            message += error.message
        }
        toast({
          title: 'Upload Error',
          description: message,
          variant: 'destructive',
        })
      })
    })

    // Add accepted files
    const newFiles: UploadFile[] = acceptedFiles.map(file => ({
      ...file,
      id: Math.random().toString(36).substr(2, 9),
      progress: 0,
      status: 'pending' as const,
    }))

    setFiles(prev => {
      const combined = [...prev, ...newFiles]
      if (combined.length > MAX_FILES) {
        toast({
          title: 'Too Many Files',
          description: `Maximum ${MAX_FILES} files allowed`,
          variant: 'destructive',
        })
        return prev
      }
      return combined
    })
  }, [toast])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: Object.keys(SUPPORTED_FORMATS).reduce((acc, mimeType) => {
      acc[mimeType] = []
      return acc
    }, {} as Record<string, string[]>),
    maxFiles: MAX_FILES,
    maxSize: MAX_TOTAL_SIZE,
    multiple: true,
  })

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId))
  }

  const uploadFiles = async () => {
    if (files.length === 0) {
      toast({
        title: 'No Files',
        description: 'Please select files to upload',
        variant: 'destructive',
      })
      return
    }

    setIsUploading(true)
    const uploadedDocuments: any[] = []

    try {
      for (const file of files) {
        if (file.status === 'success') continue

        // Update file status
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, status: 'uploading', progress: 0 } : f
        ))

        try {
          const formData = new FormData()
          formData.append('file', file)
          if (title) formData.append('title', title)
          if (tags) formData.append('tags', tags)

          // Simulate upload progress
          const progressInterval = setInterval(() => {
            setFiles(prev => prev.map(f => {
              if (f.id === file.id && f.progress < 90) {
                return { ...f, progress: f.progress + 10 }
              }
              return f
            }))
          }, 200)

          const response = await fetch('/api/v1/documents/upload', {
            method: 'POST',
            body: formData,
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
          })

          clearInterval(progressInterval)

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.detail || 'Upload failed')
          }

          const document = await response.json()
          uploadedDocuments.push(document)

          // Update file status to success
          setFiles(prev => prev.map(f => 
            f.id === file.id ? { ...f, status: 'success', progress: 100 } : f
          ))

          toast({
            title: 'Upload Successful',
            description: `${file.name} uploaded successfully`,
          })

        } catch (error) {
          // Update file status to error
          setFiles(prev => prev.map(f => 
            f.id === file.id ? { 
              ...f, 
              status: 'error', 
              progress: 0,
              error: error instanceof Error ? error.message : 'Upload failed'
            } : f
          ))

          toast({
            title: 'Upload Failed',
            description: `${file.name}: ${error instanceof Error ? error.message : 'Upload failed'}`,
            variant: 'destructive',
          })
        }
      }

      if (uploadedDocuments.length > 0) {
        onUploadComplete?.(uploadedDocuments)
        // Clear successful uploads
        setFiles(prev => prev.filter(f => f.status !== 'success'))
        setTitle('')
        setTags('')
      }

    } finally {
      setIsUploading(false)
    }
  }

  const getFileIcon = (file: File) => {
    const format = SUPPORTED_FORMATS[file.type as keyof typeof SUPPORTED_FORMATS]
    return format?.icon || '📄'
  }

  const getFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const totalSize = files.reduce((sum, file) => sum + file.size, 0)
  const hasErrors = files.some(f => f.status === 'error')
  const allUploaded = files.length > 0 && files.every(f => f.status === 'success')

  return (
    <Card className={cn('w-full max-w-4xl mx-auto', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload Documents
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Drag and Drop Area */}
        <div
          {...getRootProps()}
          className={cn(
            'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
            isDragActive 
              ? 'border-primary bg-primary/5' 
              : 'border-muted-foreground/25 hover:border-primary/50',
            isUploading && 'pointer-events-none opacity-50'
          )}
        >
          <input {...getInputProps()} />
          <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <div className="space-y-2">
            <p className="text-lg font-medium">
              {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
            </p>
            <p className="text-sm text-muted-foreground">
              or click to browse files
            </p>
            <div className="flex flex-wrap justify-center gap-2 mt-4">
              {Object.entries(SUPPORTED_FORMATS).map(([mimeType, info]) => (
                <Badge key={mimeType} variant="secondary" className="text-xs">
                  {info.ext} (max {info.maxSize}MB)
                </Badge>
              ))}
            </div>
          </div>
        </div>

        {/* File Metadata */}
        {files.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Document Title (Optional)</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter document title..."
                disabled={isUploading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="tags">Tags (Optional)</Label>
              <Input
                id="tags"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                placeholder="research, ai, machine-learning"
                disabled={isUploading}
              />
            </div>
          </div>
        )}

        {/* File List */}
        {files.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">
                Files ({files.length}/{MAX_FILES})
              </h3>
              <p className="text-sm text-muted-foreground">
                Total: {getFileSize(totalSize)}
              </p>
            </div>
            
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {files.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center gap-3 p-3 border rounded-lg"
                >
                  <span className="text-2xl">{getFileIcon(file)}</span>
                  
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{file.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {getFileSize(file.size)}
                    </p>
                    
                    {file.status === 'uploading' && (
                      <Progress value={file.progress} className="mt-2" />
                    )}
                    
                    {file.error && (
                      <p className="text-sm text-destructive mt-1">{file.error}</p>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {file.status === 'pending' && (
                      <div className="w-5 h-5 rounded-full border-2 border-muted-foreground/25" />
                    )}
                    {file.status === 'uploading' && (
                      <Loader2 className="h-5 w-5 animate-spin text-primary" />
                    )}
                    {file.status === 'success' && (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    )}
                    {file.status === 'error' && (
                      <AlertCircle className="h-5 w-5 text-destructive" />
                    )}
                    
                    {!isUploading && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.id)}
                        className="h-8 w-8 p-0"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Upload Button */}
        {files.length > 0 && (
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setFiles([])}
              disabled={isUploading}
            >
              Clear All
            </Button>
            <Button
              onClick={uploadFiles}
              disabled={isUploading || allUploaded}
              className="min-w-32"
            >
              {isUploading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : allUploaded ? (
                'All Uploaded'
              ) : (
                `Upload ${files.filter(f => f.status !== 'success').length} Files`
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
