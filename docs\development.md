# Lonors AI Agent Platform - Development Guide

## 🚀 Quick Start

### Prerequisites

- **Docker & Docker Compose**: For containerized services
- **Python 3.11+**: Backend development
- **Node.js 18+**: Frontend development
- **Git**: Version control

### Initial Setup

1. **Clone and setup the project**:
   ```bash
   git clone <repository-url>
   cd lonors
   chmod +x scripts/*.sh
   ./scripts/setup.sh
   ```

2. **Start development environment**:
   ```bash
   ./scripts/dev.sh
   ```

3. **Access the application**:
   - Frontend: http://localhost:5500
   - Backend API: http://localhost:3000
   - API Documentation: http://localhost:3000/docs

## 🏗️ Project Structure

```
lonors/
├── backend/                 # FastAPI backend
│   ├── app/                # Application code
│   │   ├── api/            # API routes
│   │   ├── core/           # Core functionality
│   │   ├── models/         # Database models
│   │   ├── schemas/        # Pydantic schemas
│   │   └── main.py         # Application entry point
│   ├── tests/              # Test suites
│   ├── alembic/            # Database migrations
│   └── pyproject.toml      # Python dependencies
├── frontend/               # Next.js frontend
│   ├── src/                # Source code
│   │   ├── app/            # Next.js app directory
│   │   ├── components/     # React components
│   │   └── lib/            # Utility libraries
│   ├── public/             # Static assets
│   └── package.json        # Node.js dependencies
├── docker/                 # Docker configurations
├── docs/                   # Documentation
├── scripts/                # Utility scripts
├── .github/                # GitHub Actions workflows
└── docker-compose.yml      # Development environment
```

## 🔧 Development Workflow

### Backend Development

1. **Navigate to backend directory**:
   ```bash
   cd backend
   ```

2. **Install dependencies**:
   ```bash
   uv sync --dev
   ```

3. **Run development server**:
   ```bash
   uv run uvicorn app.main:app --reload --port 3000
   ```

4. **Run tests**:
   ```bash
   uv run pytest
   ```

5. **Code quality checks**:
   ```bash
   uv run black .
   uv run isort .
   uv run flake8 .
   uv run mypy .
   ```

### Frontend Development

1. **Navigate to frontend directory**:
   ```bash
   cd frontend
   ```

2. **Install dependencies**:
   ```bash
   pnpm install
   ```

3. **Run development server**:
   ```bash
   pnpm dev
   ```

4. **Run tests**:
   ```bash
   pnpm test
   ```

5. **Code quality checks**:
   ```bash
   pnpm lint
   pnpm type-check
   pnpm format:check
   ```

## 🗄️ Database Management

### Migrations

1. **Create a new migration**:
   ```bash
   cd backend
   uv run alembic revision --autogenerate -m "Description of changes"
   ```

2. **Apply migrations**:
   ```bash
   uv run alembic upgrade head
   ```

3. **Rollback migrations**:
   ```bash
   uv run alembic downgrade -1
   ```

### Database Access

- **PostgreSQL**: `localhost:5432`
  - Database: `lonors`
  - Username: `lonors`
  - Password: `lonors_dev_password`

- **Qdrant Vector DB**: `http://localhost:6333`
- **DragonflyDB Cache**: `localhost:6379`
- **Neo4j Graph DB**: `http://localhost:7474`
  - Username: `neo4j`
  - Password: `lonors_dev_password`

## 🧪 Testing

### Running Tests

1. **All tests**:
   ```bash
   ./scripts/test.sh
   ```

2. **Backend only**:
   ```bash
   ./scripts/test.sh --backend-only
   ```

3. **Frontend only**:
   ```bash
   ./scripts/test.sh --frontend-only
   ```

4. **With coverage**:
   ```bash
   ./scripts/test.sh --coverage
   ```

### Test Structure

- **Unit Tests**: Test individual functions and classes
- **Integration Tests**: Test API endpoints and database interactions
- **End-to-End Tests**: Test complete user workflows

### Writing Tests

1. **Backend tests** (pytest):
   ```python
   import pytest
   from httpx import AsyncClient
   
   @pytest.mark.asyncio
   async def test_endpoint(async_client: AsyncClient):
       response = await async_client.get("/api/v1/endpoint")
       assert response.status_code == 200
   ```

2. **Frontend tests** (Jest + React Testing Library):
   ```typescript
   import { render, screen } from '@testing-library/react'
   import Component from './Component'
   
   test('renders component', () => {
     render(<Component />)
     expect(screen.getByText('Hello')).toBeInTheDocument()
   })
   ```

## 🔒 Security

### Authentication

- **JWT Tokens**: Access and refresh tokens
- **Password Hashing**: bcrypt with salt
- **Role-Based Access Control**: User, Moderator, Admin, Superuser

### Security Headers

- Content Security Policy (CSP)
- X-Frame-Options
- X-Content-Type-Options
- Strict-Transport-Security (HTTPS)

### Environment Variables

Never commit sensitive data. Use environment variables:

```bash
# Backend (.env)
SECRET_KEY=your-secret-key
DATABASE_URL=postgresql://...
OPENAI_API_KEY=sk-...

# Frontend (.env.local)
NEXT_PUBLIC_API_URL=http://localhost:3000
```

## 📊 Monitoring

### Development Monitoring

- **Logs**: Structured JSON logging with correlation IDs
- **Metrics**: Prometheus metrics at `/metrics`
- **Health Checks**: `/health` and `/health/detailed`

### Production Monitoring

- **Grafana**: http://localhost:3001 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Sentry**: Error tracking and performance monitoring

## 🚀 Deployment

### Docker Deployment

1. **Build images**:
   ```bash
   docker-compose build
   ```

2. **Run in production mode**:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

### Environment-Specific Configurations

- **Development**: Local development with hot reload
- **Staging**: Production-like environment for testing
- **Production**: Optimized for performance and security

## 🔧 Troubleshooting

### Common Issues

1. **Port conflicts**:
   ```bash
   # Check what's using a port
   lsof -i :3000
   
   # Kill process
   kill -9 <PID>
   ```

2. **Database connection issues**:
   ```bash
   # Check database status
   docker-compose ps
   
   # View database logs
   docker-compose logs postgres
   ```

3. **Permission issues**:
   ```bash
   # Make scripts executable
   chmod +x scripts/*.sh
   ```

### Debug Mode

1. **Backend debug**:
   ```bash
   cd backend
   uv run python -m debugpy --listen 5678 --wait-for-client -m uvicorn app.main:app --reload
   ```

2. **Frontend debug**:
   ```bash
   cd frontend
   NODE_OPTIONS='--inspect' pnpm dev
   ```

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Next.js Documentation](https://nextjs.org/docs)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
- [Pydantic Documentation](https://docs.pydantic.dev/)
- [Docker Documentation](https://docs.docker.com/)

## 🤝 Contributing

1. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make changes and test**:
   ```bash
   ./scripts/test.sh
   ```

3. **Commit with conventional commits**:
   ```bash
   git commit -m "feat: add new feature"
   ```

4. **Push and create PR**:
   ```bash
   git push origin feature/your-feature-name
   ```

## 📝 Code Style

### Backend (Python)

- **Formatter**: Black
- **Import Sorting**: isort
- **Linting**: flake8
- **Type Checking**: mypy
- **Security**: bandit

### Frontend (TypeScript)

- **Formatter**: Prettier
- **Linting**: ESLint
- **Type Checking**: TypeScript compiler
- **Styling**: TailwindCSS

### Commit Messages

Follow [Conventional Commits](https://www.conventionalcommits.org/):

- `feat:` New features
- `fix:` Bug fixes
- `docs:` Documentation changes
- `style:` Code style changes
- `refactor:` Code refactoring
- `test:` Test additions/changes
- `chore:` Maintenance tasks
