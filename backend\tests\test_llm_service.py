"""Tests for LLM service."""

import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

from app.services.llm_service import LLMService, llm_service
from app.core.exceptions import ProcessingError


class TestLLMService:
    """Test cases for LLM service."""
    
    @pytest.fixture
    def llm_service_instance(self):
        """Create LLM service instance for testing."""
        return LLMService()
    
    @pytest.fixture
    def sample_query(self):
        """Sample query for testing."""
        return "What are the main benefits of artificial intelligence?"
    
    @pytest.fixture
    def sample_context(self):
        """Sample context chunks for testing."""
        return [
            {
                "content": "Artificial intelligence offers numerous benefits including automation of repetitive tasks.",
                "source": "AI Benefits Document (p. 1)",
                "similarity_score": 0.9
            },
            {
                "content": "Machine learning algorithms can improve decision-making processes in businesses.",
                "source": "ML Applications Document (p. 3)",
                "similarity_score": 0.8
            }
        ]
    
    @pytest.fixture
    def mock_openai_response(self):
        """Mock OpenAI API response."""
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = "AI provides automation, efficiency, and improved decision-making capabilities."
        mock_response.usage.prompt_tokens = 100
        mock_response.usage.completion_tokens = 50
        mock_response.usage.total_tokens = 150
        return mock_response
    
    @pytest.fixture
    def mock_anthropic_response(self):
        """Mock Anthropic API response."""
        mock_response = MagicMock()
        mock_response.content = [MagicMock()]
        mock_response.content[0].text = "AI offers significant advantages in automation and analysis."
        mock_response.usage.input_tokens = 100
        mock_response.usage.output_tokens = 40
        return mock_response
    
    @pytest.mark.asyncio
    async def test_service_initialization(self, llm_service_instance):
        """Test LLM service initialization."""
        assert llm_service_instance is not None
        
        # Test initialization without API keys
        with patch('app.core.config.get_settings') as mock_settings:
            mock_settings.return_value.OPENAI_API_KEY = None
            mock_settings.return_value.ANTHROPIC_API_KEY = None
            
            service = LLMService()
            assert service.openai_client is None
            assert service.anthropic_client is None
    
    @pytest.mark.asyncio
    async def test_openai_chat_response(self, llm_service_instance, sample_query, sample_context, mock_openai_response):
        """Test OpenAI chat response generation."""
        # Mock OpenAI client
        mock_client = AsyncMock()
        mock_client.chat.completions.create.return_value = mock_openai_response
        llm_service_instance.openai_client = mock_client
        
        with patch('time.time', side_effect=[0, 1.5]):  # Mock processing time
            result = await llm_service_instance.generate_chat_response(
                query=sample_query,
                context=sample_context,
                model="gpt-4",
                max_tokens=1000,
                temperature=0.7
            )
        
        assert "response" in result
        assert "model_used" in result
        assert "token_usage" in result
        assert "processing_time" in result
        assert result["model_used"] == "gpt-4"
        assert result["response"] == "AI provides automation, efficiency, and improved decision-making capabilities."
        assert result["token_usage"]["total_tokens"] == 150
    
    @pytest.mark.asyncio
    async def test_anthropic_chat_response(self, llm_service_instance, sample_query, sample_context, mock_anthropic_response):
        """Test Anthropic chat response generation."""
        # Mock Anthropic client
        mock_client = AsyncMock()
        mock_client.messages.create.return_value = mock_anthropic_response
        llm_service_instance.anthropic_client = mock_client
        
        with patch('time.time', side_effect=[0, 2.0]):  # Mock processing time
            result = await llm_service_instance.generate_chat_response(
                query=sample_query,
                context=sample_context,
                model="claude-3-sonnet-20240229",
                max_tokens=1000,
                temperature=0.7
            )
        
        assert "response" in result
        assert "model_used" in result
        assert "token_usage" in result
        assert result["model_used"] == "claude-3-sonnet-20240229"
        assert result["response"] == "AI offers significant advantages in automation and analysis."
        assert result["token_usage"]["total_tokens"] == 140  # 100 + 40
    
    @pytest.mark.asyncio
    async def test_auto_model_selection(self, llm_service_instance):
        """Test automatic model selection."""
        # Mock both clients available
        llm_service_instance.openai_client = MagicMock()
        llm_service_instance.anthropic_client = MagicMock()
        
        # Test short query - should prefer GPT-4
        short_query = "What is AI?"
        short_context = [{"content": "AI is artificial intelligence."}]
        
        model = await llm_service_instance._select_best_model(short_query, short_context)
        assert model == "gpt-4-turbo-preview"
        
        # Test long query with complex reasoning - should prefer Claude
        long_query = "Please analyze and compare the complex implications of artificial intelligence"
        long_context = [{"content": "Very long context " * 1000}]
        
        model = await llm_service_instance._select_best_model(long_query, long_context)
        assert model == "claude-3-sonnet-20240229"
    
    @pytest.mark.asyncio
    async def test_context_formatting(self, llm_service_instance, sample_context):
        """Test context formatting for prompts."""
        formatted = llm_service_instance._format_context(sample_context)
        
        assert "Context 1" in formatted
        assert "Context 2" in formatted
        assert "AI Benefits Document" in formatted
        assert "ML Applications Document" in formatted
        assert "0.90" in formatted  # Similarity score
        assert "0.80" in formatted
    
    @pytest.mark.asyncio
    async def test_prompt_creation(self, llm_service_instance, sample_query):
        """Test chat prompt creation."""
        context = "Sample context about AI benefits."
        prompt = llm_service_instance._create_chat_prompt(sample_query, context)
        
        assert sample_query in prompt
        assert context in prompt
        assert "document assistant" in prompt.lower()
        assert "instructions:" in prompt.lower()
    
    @pytest.mark.asyncio
    async def test_llm_summarization(self, llm_service_instance, mock_openai_response):
        """Test LLM-based summarization."""
        text = "This is a long document that needs to be summarized. " * 50
        
        # Mock OpenAI client
        mock_client = AsyncMock()
        mock_openai_response.choices[0].message.content = "This is a concise summary of the document."
        mock_client.chat.completions.create.return_value = mock_openai_response
        llm_service_instance.openai_client = mock_client
        
        result = await llm_service_instance.summarize_with_llm(
            text=text,
            style="concise",
            max_length=500
        )
        
        assert "summary" in result
        assert "style" in result
        assert "model_used" in result
        assert "compression_ratio" in result
        assert result["style"] == "concise"
        assert result["summary"] == "This is a concise summary of the document."
    
    @pytest.mark.asyncio
    async def test_summary_prompt_creation(self, llm_service_instance):
        """Test summary prompt creation."""
        text = "Sample text to summarize."
        style = "bullet-points"
        max_length = 200
        
        prompt = llm_service_instance._create_summary_prompt(text, style, max_length)
        
        assert text in prompt
        assert "bullet points" in prompt.lower()
        assert "200" in prompt
        assert "summarize" in prompt.lower()
    
    @pytest.mark.asyncio
    async def test_token_counting(self, llm_service_instance):
        """Test token counting functionality."""
        # Mock tokenizer
        mock_tokenizer = MagicMock()
        mock_tokenizer.encode.return_value = [1, 2, 3, 4, 5]  # 5 tokens
        llm_service_instance.tokenizer = mock_tokenizer
        
        text = "This is a test sentence."
        token_count = llm_service_instance.count_tokens(text)
        
        assert token_count == 5
        
        # Test fallback when tokenizer fails
        llm_service_instance.tokenizer = None
        token_count = llm_service_instance.count_tokens(text)
        assert token_count > 0  # Should use word-based estimation
    
    @pytest.mark.asyncio
    async def test_available_models(self, llm_service_instance):
        """Test getting available models."""
        # Mock both clients
        llm_service_instance.openai_client = MagicMock()
        llm_service_instance.anthropic_client = MagicMock()
        
        models = await llm_service_instance.get_available_models()
        
        assert "openai" in models
        assert "anthropic" in models
        assert len(models["openai"]) > 0
        assert len(models["anthropic"]) > 0
        assert "gpt-4" in models["openai"]
        assert "claude-3-sonnet-20240229" in models["anthropic"]
    
    @pytest.mark.asyncio
    async def test_error_handling_no_clients(self, llm_service_instance):
        """Test error handling when no clients are available."""
        llm_service_instance.openai_client = None
        llm_service_instance.anthropic_client = None
        
        with pytest.raises(ProcessingError):
            await llm_service_instance.generate_chat_response(
                query="test query",
                context=[],
                model="auto"
            )
    
    @pytest.mark.asyncio
    async def test_error_handling_api_failure(self, llm_service_instance, sample_query, sample_context):
        """Test error handling when API calls fail."""
        # Mock OpenAI client to raise exception
        mock_client = AsyncMock()
        mock_client.chat.completions.create.side_effect = Exception("API Error")
        llm_service_instance.openai_client = mock_client
        
        with pytest.raises(ProcessingError):
            await llm_service_instance.generate_chat_response(
                query=sample_query,
                context=sample_context,
                model="gpt-4"
            )
    
    @pytest.mark.asyncio
    async def test_unsupported_model(self, llm_service_instance, sample_query, sample_context):
        """Test error handling for unsupported models."""
        with pytest.raises(ProcessingError):
            await llm_service_instance.generate_chat_response(
                query=sample_query,
                context=sample_context,
                model="unsupported-model"
            )
    
    @pytest.mark.asyncio
    async def test_health_check_healthy(self, llm_service_instance):
        """Test health check with healthy services."""
        # Mock successful API calls
        mock_openai_client = AsyncMock()
        mock_openai_response = MagicMock()
        mock_openai_response.choices = [MagicMock()]
        mock_openai_client.chat.completions.create.return_value = mock_openai_response
        llm_service_instance.openai_client = mock_openai_client
        
        mock_anthropic_client = AsyncMock()
        mock_anthropic_response = MagicMock()
        mock_anthropic_response.content = [MagicMock()]
        mock_anthropic_client.messages.create.return_value = mock_anthropic_response
        llm_service_instance.anthropic_client = mock_anthropic_client
        
        health_status = await llm_service_instance.health_check()
        
        assert "status" in health_status
        assert "providers" in health_status
        assert health_status["providers"]["openai"] == "healthy"
        assert health_status["providers"]["anthropic"] == "healthy"
        assert health_status["status"] == "healthy"
    
    @pytest.mark.asyncio
    async def test_health_check_unhealthy(self, llm_service_instance):
        """Test health check with unhealthy services."""
        # Mock failed API calls
        mock_openai_client = AsyncMock()
        mock_openai_client.chat.completions.create.side_effect = Exception("API Error")
        llm_service_instance.openai_client = mock_openai_client
        
        llm_service_instance.anthropic_client = None  # Not configured
        
        health_status = await llm_service_instance.health_check()
        
        assert health_status["status"] == "degraded"
        assert "unhealthy" in health_status["providers"]["openai"]
        assert health_status["providers"]["anthropic"] == "not_configured"
    
    @pytest.mark.asyncio
    async def test_health_check_not_configured(self, llm_service_instance):
        """Test health check when no services are configured."""
        llm_service_instance.openai_client = None
        llm_service_instance.anthropic_client = None
        
        health_status = await llm_service_instance.health_check()
        
        assert health_status["status"] == "not_configured"
        assert health_status["providers"]["openai"] == "not_configured"
        assert health_status["providers"]["anthropic"] == "not_configured"
    
    @pytest.mark.asyncio
    async def test_global_instance(self):
        """Test that global instance is properly initialized."""
        assert llm_service is not None
        assert isinstance(llm_service, LLMService)
    
    @pytest.mark.asyncio
    async def test_context_length_handling(self, llm_service_instance):
        """Test handling of very long contexts."""
        # Create very long context
        long_context = [
            {
                "content": "Very long content " * 1000,
                "source": "Long Document",
                "similarity_score": 0.9
            }
        ]
        
        formatted = llm_service_instance._format_context(long_context)
        assert len(formatted) > 10000  # Should be very long
        
        # Test that it doesn't break the service
        assert isinstance(formatted, str)
    
    @pytest.mark.asyncio
    async def test_temperature_and_max_tokens_parameters(self, llm_service_instance, mock_openai_response):
        """Test that temperature and max_tokens parameters are properly passed."""
        mock_client = AsyncMock()
        mock_client.chat.completions.create.return_value = mock_openai_response
        llm_service_instance.openai_client = mock_client
        
        await llm_service_instance.generate_chat_response(
            query="test",
            context=[],
            model="gpt-4",
            max_tokens=500,
            temperature=0.3
        )
        
        # Verify the parameters were passed correctly
        call_args = mock_client.chat.completions.create.call_args
        assert call_args.kwargs["max_tokens"] == 500
        assert call_args.kwargs["temperature"] == 0.3
    
    @pytest.mark.asyncio
    async def test_empty_context_handling(self, llm_service_instance):
        """Test handling of empty context."""
        empty_context = []
        formatted = llm_service_instance._format_context(empty_context)
        
        assert "No relevant context found" in formatted
    
    @pytest.mark.asyncio
    async def test_model_fallback_logic(self, llm_service_instance):
        """Test model fallback when preferred model is not available."""
        # Only OpenAI available
        llm_service_instance.openai_client = MagicMock()
        llm_service_instance.anthropic_client = None
        
        model = await llm_service_instance._select_best_model("complex analysis query", [])
        assert model.startswith("gpt")
        
        # Only Anthropic available
        llm_service_instance.openai_client = None
        llm_service_instance.anthropic_client = MagicMock()
        
        model = await llm_service_instance._select_best_model("simple query", [])
        assert model.startswith("claude")


@pytest.mark.integration
class TestLLMServiceIntegration:
    """Integration tests for LLM service."""
    
    @pytest.mark.asyncio
    async def test_real_openai_integration(self):
        """Test integration with real OpenAI API."""
        # This test would require actual API keys and credits
        pytest.skip("Requires actual OpenAI API key and credits")
    
    @pytest.mark.asyncio
    async def test_real_anthropic_integration(self):
        """Test integration with real Anthropic API."""
        # This test would require actual API keys and credits
        pytest.skip("Requires actual Anthropic API key and credits")
    
    @pytest.mark.asyncio
    async def test_rate_limiting_handling(self):
        """Test handling of API rate limits."""
        # This test would check rate limit handling
        pytest.skip("Requires rate limit testing setup")
