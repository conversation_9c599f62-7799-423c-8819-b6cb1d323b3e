"""Advanced AI features for document analysis, summarization, and categorization."""

import asyncio
import json
from typing import Dict, List, Optional, Tuple

import numpy as np
from sklearn.cluster import KMeans
from sklearn.feature_extraction.text import TfidfVectorizer
import structlog

from app.core.config import get_settings
from app.core.exceptions import ProcessingError
from app.services.embedding_service import embedding_service

settings = get_settings()
logger = structlog.get_logger(__name__)


class AIFeaturesService:
    """Service for advanced AI features including summarization and categorization."""
    
    def __init__(self):
        """Initialize AI features service."""
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2)
        )
        self.category_cache = {}
        
    async def summarize_document(
        self, 
        text: str, 
        max_length: int = 500,
        style: str = "extractive"
    ) -> Dict:
        """
        Generate document summary using extractive or abstractive methods.
        
        Args:
            text: Document text to summarize
            max_length: Maximum summary length
            style: Summary style ('extractive' or 'abstractive')
            
        Returns:
            Dict: Summary with metadata
        """
        logger.info("Generating document summary", style=style, text_length=len(text))
        
        try:
            if style == "extractive":
                summary_data = await self._extractive_summarization(text, max_length)
            elif style == "abstractive":
                summary_data = await self._abstractive_summarization(text, max_length)
            else:
                raise ProcessingError(f"Unsupported summarization style: {style}")
            
            # Add metadata
            summary_data.update({
                "original_length": len(text),
                "compression_ratio": len(summary_data["summary"]) / len(text),
                "style": style,
                "word_count": len(summary_data["summary"].split())
            })
            
            logger.info(
                "Document summarization completed",
                style=style,
                compression_ratio=summary_data["compression_ratio"]
            )
            
            return summary_data
            
        except Exception as e:
            logger.error("Document summarization failed", error=str(e))
            raise ProcessingError(f"Summarization failed: {str(e)}")
    
    async def _extractive_summarization(self, text: str, max_length: int) -> Dict:
        """Generate extractive summary using sentence ranking."""
        # Split text into sentences
        sentences = self._split_into_sentences(text)
        
        if len(sentences) <= 3:
            return {
                "summary": text[:max_length],
                "key_sentences": sentences,
                "method": "extractive_simple"
            }
        
        # Generate embeddings for sentences
        sentence_embeddings = await embedding_service.generate_embeddings_batch(sentences)
        
        # Calculate sentence importance scores
        sentence_scores = await self._calculate_sentence_importance(
            sentences, sentence_embeddings
        )
        
        # Select top sentences
        top_sentences = await self._select_top_sentences(
            sentences, sentence_scores, max_length
        )
        
        summary = " ".join(top_sentences)
        
        return {
            "summary": summary,
            "key_sentences": top_sentences,
            "sentence_scores": sentence_scores,
            "method": "extractive_embedding"
        }
    
    async def _abstractive_summarization(self, text: str, max_length: int) -> Dict:
        """Generate abstractive summary (placeholder for LLM integration)."""
        # This would integrate with external LLM services
        # For now, return a sophisticated extractive summary
        logger.info("Abstractive summarization requested, using advanced extractive method")
        
        # Use extractive method with additional processing
        extractive_result = await self._extractive_summarization(text, max_length * 2)
        
        # Post-process to make it more abstractive-like
        summary = await self._refine_extractive_summary(
            extractive_result["summary"], max_length
        )
        
        return {
            "summary": summary,
            "key_concepts": await self._extract_key_concepts(text),
            "method": "abstractive_simulated"
        }
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences."""
        import re
        
        # Simple sentence splitting (can be improved with NLTK)
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        return sentences
    
    async def _calculate_sentence_importance(
        self, 
        sentences: List[str], 
        embeddings: List[List[float]]
    ) -> List[float]:
        """Calculate importance scores for sentences."""
        # Convert embeddings to numpy array
        embeddings_array = np.array(embeddings)
        
        # Calculate centroid (document representation)
        centroid = np.mean(embeddings_array, axis=0)
        
        # Calculate similarity to centroid (importance score)
        scores = []
        for embedding in embeddings_array:
            similarity = np.dot(embedding, centroid) / (
                np.linalg.norm(embedding) * np.linalg.norm(centroid)
            )
            scores.append(float(similarity))
        
        return scores
    
    async def _select_top_sentences(
        self, 
        sentences: List[str], 
        scores: List[float], 
        max_length: int
    ) -> List[str]:
        """Select top sentences based on scores and length constraint."""
        # Sort sentences by score
        sentence_score_pairs = list(zip(sentences, scores))
        sentence_score_pairs.sort(key=lambda x: x[1], reverse=True)
        
        # Select sentences within length limit
        selected_sentences = []
        current_length = 0
        
        for sentence, score in sentence_score_pairs:
            if current_length + len(sentence) <= max_length:
                selected_sentences.append(sentence)
                current_length += len(sentence)
            
            if current_length >= max_length * 0.9:  # 90% of max length
                break
        
        # Maintain original order
        original_order_sentences = []
        for sentence in sentences:
            if sentence in selected_sentences:
                original_order_sentences.append(sentence)
        
        return original_order_sentences
    
    async def _refine_extractive_summary(self, summary: str, max_length: int) -> str:
        """Refine extractive summary to be more coherent."""
        # Simple refinement - can be enhanced with NLP techniques
        sentences = self._split_into_sentences(summary)
        
        # Remove redundant sentences
        unique_sentences = []
        for sentence in sentences:
            is_redundant = False
            for existing in unique_sentences:
                # Simple similarity check
                if len(set(sentence.lower().split()) & set(existing.lower().split())) > len(sentence.split()) * 0.7:
                    is_redundant = True
                    break
            
            if not is_redundant:
                unique_sentences.append(sentence)
        
        refined_summary = " ".join(unique_sentences)
        
        # Truncate if necessary
        if len(refined_summary) > max_length:
            refined_summary = refined_summary[:max_length].rsplit(' ', 1)[0] + "..."
        
        return refined_summary
    
    async def _extract_key_concepts(self, text: str) -> List[str]:
        """Extract key concepts from text."""
        try:
            # Use TF-IDF to find important terms
            tfidf_matrix = self.tfidf_vectorizer.fit_transform([text])
            feature_names = self.tfidf_vectorizer.get_feature_names_out()
            tfidf_scores = tfidf_matrix.toarray()[0]
            
            # Get top terms
            top_indices = np.argsort(tfidf_scores)[-10:][::-1]
            key_concepts = [feature_names[i] for i in top_indices if tfidf_scores[i] > 0]
            
            return key_concepts
            
        except Exception as e:
            logger.warning("Key concept extraction failed", error=str(e))
            return []
    
    async def categorize_document(
        self, 
        text: str, 
        categories: Optional[List[str]] = None
    ) -> Dict:
        """
        Automatically categorize document based on content.
        
        Args:
            text: Document text to categorize
            categories: Optional predefined categories
            
        Returns:
            Dict: Categorization results with confidence scores
        """
        logger.info("Categorizing document", text_length=len(text))
        
        try:
            if categories:
                # Supervised categorization with predefined categories
                result = await self._supervised_categorization(text, categories)
            else:
                # Unsupervised categorization
                result = await self._unsupervised_categorization(text)
            
            logger.info(
                "Document categorization completed",
                predicted_category=result.get("category"),
                confidence=result.get("confidence")
            )
            
            return result
            
        except Exception as e:
            logger.error("Document categorization failed", error=str(e))
            raise ProcessingError(f"Categorization failed: {str(e)}")
    
    async def _supervised_categorization(
        self, 
        text: str, 
        categories: List[str]
    ) -> Dict:
        """Categorize document using predefined categories."""
        # Generate embedding for the document
        doc_embedding = await embedding_service.generate_embedding(text)
        
        # Generate embeddings for category descriptions
        category_embeddings = await embedding_service.generate_embeddings_batch(categories)
        
        # Calculate similarities
        similarities = []
        for cat_embedding in category_embeddings:
            similarity = np.dot(doc_embedding, cat_embedding) / (
                np.linalg.norm(doc_embedding) * np.linalg.norm(cat_embedding)
            )
            similarities.append(float(similarity))
        
        # Find best match
        best_idx = np.argmax(similarities)
        best_category = categories[best_idx]
        confidence = similarities[best_idx]
        
        return {
            "category": best_category,
            "confidence": confidence,
            "all_scores": dict(zip(categories, similarities)),
            "method": "supervised_embedding"
        }
    
    async def _unsupervised_categorization(self, text: str) -> Dict:
        """Categorize document using unsupervised methods."""
        # Extract key topics using TF-IDF and clustering
        try:
            # Get TF-IDF features
            tfidf_matrix = self.tfidf_vectorizer.fit_transform([text])
            feature_names = self.tfidf_vectorizer.get_feature_names_out()
            tfidf_scores = tfidf_matrix.toarray()[0]
            
            # Get top terms as potential categories
            top_indices = np.argsort(tfidf_scores)[-5:][::-1]
            top_terms = [feature_names[i] for i in top_indices if tfidf_scores[i] > 0]
            
            # Generate a category based on top terms
            if top_terms:
                category = self._generate_category_from_terms(top_terms)
            else:
                category = "general"
            
            return {
                "category": category,
                "confidence": 0.7,  # Default confidence for unsupervised
                "top_terms": top_terms,
                "method": "unsupervised_tfidf"
            }
            
        except Exception as e:
            logger.warning("Unsupervised categorization failed", error=str(e))
            return {
                "category": "unknown",
                "confidence": 0.0,
                "method": "fallback"
            }
    
    def _generate_category_from_terms(self, terms: List[str]) -> str:
        """Generate category name from top terms."""
        # Simple heuristic-based category generation
        term_categories = {
            "technology": ["software", "computer", "digital", "tech", "system", "data"],
            "business": ["company", "market", "financial", "business", "revenue", "profit"],
            "science": ["research", "study", "analysis", "experiment", "scientific"],
            "education": ["learning", "student", "education", "academic", "university"],
            "health": ["health", "medical", "patient", "treatment", "clinical"],
            "legal": ["legal", "law", "court", "contract", "agreement"],
        }
        
        # Count matches for each category
        category_scores = {}
        for category, keywords in term_categories.items():
            score = sum(1 for term in terms if any(keyword in term.lower() for keyword in keywords))
            if score > 0:
                category_scores[category] = score
        
        if category_scores:
            return max(category_scores, key=category_scores.get)
        else:
            # Use the first term as category
            return terms[0] if terms else "general"
    
    async def analyze_document_sentiment(self, text: str) -> Dict:
        """
        Analyze document sentiment and emotional tone.
        
        Args:
            text: Document text to analyze
            
        Returns:
            Dict: Sentiment analysis results
        """
        logger.info("Analyzing document sentiment", text_length=len(text))
        
        try:
            # Simple sentiment analysis using word-based approach
            # In production, this would use more sophisticated models
            
            positive_words = {
                "good", "great", "excellent", "positive", "success", "achieve", 
                "improve", "benefit", "advantage", "effective", "efficient"
            }
            
            negative_words = {
                "bad", "poor", "negative", "fail", "problem", "issue", "difficult",
                "challenge", "risk", "concern", "disadvantage", "ineffective"
            }
            
            words = text.lower().split()
            positive_count = sum(1 for word in words if word in positive_words)
            negative_count = sum(1 for word in words if word in negative_words)
            
            total_sentiment_words = positive_count + negative_count
            
            if total_sentiment_words == 0:
                sentiment = "neutral"
                confidence = 0.5
            else:
                sentiment_score = (positive_count - negative_count) / total_sentiment_words
                
                if sentiment_score > 0.2:
                    sentiment = "positive"
                elif sentiment_score < -0.2:
                    sentiment = "negative"
                else:
                    sentiment = "neutral"
                
                confidence = min(abs(sentiment_score) + 0.5, 1.0)
            
            return {
                "sentiment": sentiment,
                "confidence": confidence,
                "positive_indicators": positive_count,
                "negative_indicators": negative_count,
                "sentiment_score": sentiment_score if total_sentiment_words > 0 else 0.0
            }
            
        except Exception as e:
            logger.error("Sentiment analysis failed", error=str(e))
            return {
                "sentiment": "unknown",
                "confidence": 0.0,
                "error": str(e)
            }
    
    async def extract_entities(self, text: str) -> Dict:
        """
        Extract named entities from document text.
        
        Args:
            text: Document text to analyze
            
        Returns:
            Dict: Extracted entities by type
        """
        logger.info("Extracting entities from document", text_length=len(text))
        
        try:
            # Simple entity extraction using patterns
            # In production, this would use NER models like spaCy or transformers
            
            import re
            
            entities = {
                "emails": [],
                "urls": [],
                "phone_numbers": [],
                "dates": [],
                "organizations": [],
                "locations": []
            }
            
            # Email pattern
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            entities["emails"] = re.findall(email_pattern, text)
            
            # URL pattern
            url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
            entities["urls"] = re.findall(url_pattern, text)
            
            # Phone number pattern (simple)
            phone_pattern = r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'
            entities["phone_numbers"] = re.findall(phone_pattern, text)
            
            # Date pattern (simple)
            date_pattern = r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b'
            entities["dates"] = re.findall(date_pattern, text)
            
            # Remove duplicates
            for entity_type in entities:
                entities[entity_type] = list(set(entities[entity_type]))
            
            return {
                "entities": entities,
                "total_entities": sum(len(v) for v in entities.values())
            }
            
        except Exception as e:
            logger.error("Entity extraction failed", error=str(e))
            return {
                "entities": {},
                "total_entities": 0,
                "error": str(e)
            }
    
    async def health_check(self) -> Dict:
        """Check health of AI features service."""
        try:
            # Test basic functionality
            test_text = "This is a test document for health checking."
            
            # Test summarization
            summary_result = await self.summarize_document(test_text, max_length=50)
            
            # Test categorization
            category_result = await self.categorize_document(test_text)
            
            return {
                "status": "healthy",
                "components": {
                    "summarization": "healthy" if summary_result else "unhealthy",
                    "categorization": "healthy" if category_result else "unhealthy",
                    "tfidf_vectorizer": "healthy" if self.tfidf_vectorizer else "unhealthy"
                }
            }
            
        except Exception as e:
            logger.error("AI features health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "error": str(e)
            }


# Global instance
ai_features_service = AIFeaturesService()
