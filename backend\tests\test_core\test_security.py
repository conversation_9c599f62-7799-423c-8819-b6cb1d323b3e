"""Tests for core security functionality."""

import pytest
from datetime import datetime, timedelta

from app.core.exceptions import AuthenticationError
from app.core.security import (
    create_access_token,
    create_refresh_token,
    get_password_hash,
    verify_password,
    verify_token,
    generate_password_reset_token,
    verify_password_reset_token,
    SecurityValidator,
)


class TestPasswordHashing:
    """Test password hashing functionality."""
    
    def test_password_hashing(self):
        """Test password hashing and verification."""
        password = "TestPassword123!"
        hashed = get_password_hash(password)
        
        # Hash should be different from original password
        assert hashed != password
        
        # Verification should work
        assert verify_password(password, hashed) is True
        
        # Wrong password should fail
        assert verify_password("WrongPassword", hashed) is False
    
    def test_different_hashes_for_same_password(self):
        """Test that same password produces different hashes."""
        password = "TestPassword123!"
        hash1 = get_password_hash(password)
        hash2 = get_password_hash(password)
        
        # Hashes should be different (due to salt)
        assert hash1 != hash2
        
        # Both should verify correctly
        assert verify_password(password, hash1) is True
        assert verify_password(password, hash2) is True


class TestJWTTokens:
    """Test JWT token functionality."""
    
    def test_create_and_verify_access_token(self):
        """Test access token creation and verification."""
        user_id = 123
        token = create_access_token(subject=user_id)
        
        # Token should be a string
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Verify token
        payload = verify_token(token, token_type="access")
        assert payload["sub"] == str(user_id)
        assert payload["type"] == "access"
        assert "exp" in payload
        assert "iat" in payload
    
    def test_create_and_verify_refresh_token(self):
        """Test refresh token creation and verification."""
        user_id = 123
        token = create_refresh_token(subject=user_id)
        
        # Token should be a string
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Verify token
        payload = verify_token(token, token_type="refresh")
        assert payload["sub"] == str(user_id)
        assert payload["type"] == "refresh"
    
    def test_token_with_additional_claims(self):
        """Test token creation with additional claims."""
        user_id = 123
        additional_claims = {
            "username": "testuser",
            "role": "admin",
            "permissions": ["read", "write"],
        }
        
        token = create_access_token(subject=user_id, additional_claims=additional_claims)
        payload = verify_token(token, token_type="access")
        
        assert payload["username"] == "testuser"
        assert payload["role"] == "admin"
        assert payload["permissions"] == ["read", "write"]
    
    def test_token_expiration(self):
        """Test token expiration."""
        user_id = 123
        # Create token that expires in 1 second
        expires_delta = timedelta(seconds=1)
        token = create_access_token(subject=user_id, expires_delta=expires_delta)
        
        # Token should be valid immediately
        payload = verify_token(token, token_type="access")
        assert payload["sub"] == str(user_id)
        
        # Wait for token to expire
        import time
        time.sleep(2)
        
        # Token should now be expired
        with pytest.raises(AuthenticationError, match="Token has expired"):
            verify_token(token, token_type="access")
    
    def test_invalid_token(self):
        """Test verification of invalid tokens."""
        # Invalid token format
        with pytest.raises(AuthenticationError, match="Invalid token"):
            verify_token("invalid_token", token_type="access")
        
        # Wrong token type
        refresh_token = create_refresh_token(subject=123)
        with pytest.raises(AuthenticationError, match="Invalid token type"):
            verify_token(refresh_token, token_type="access")


class TestPasswordReset:
    """Test password reset functionality."""
    
    def test_generate_and_verify_password_reset_token(self):
        """Test password reset token generation and verification."""
        email = "<EMAIL>"
        token = generate_password_reset_token(email)
        
        # Token should be a string
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Verify token
        verified_email = verify_password_reset_token(token)
        assert verified_email == email
    
    def test_invalid_password_reset_token(self):
        """Test verification of invalid password reset tokens."""
        # Invalid token
        result = verify_password_reset_token("invalid_token")
        assert result is None
        
        # Expired token (create token that expires immediately)
        email = "<EMAIL>"
        # This would require mocking datetime to test expiration
        # For now, just test invalid format
        result = verify_password_reset_token("clearly.invalid.token")
        assert result is None


class TestSecurityValidator:
    """Test security validation utilities."""
    
    def test_password_strength_validation(self):
        """Test password strength validation."""
        # Strong password
        is_valid, issues = SecurityValidator.validate_password_strength("StrongPass123!")
        assert is_valid is True
        assert len(issues) == 0
        
        # Weak passwords
        weak_passwords = [
            ("short", ["Password must be at least 8 characters long"]),
            ("nouppercase123!", ["Password must contain at least one uppercase letter"]),
            ("NOLOWERCASE123!", ["Password must contain at least one lowercase letter"]),
            ("NoNumbers!", ["Password must contain at least one digit"]),
            ("NoSpecialChars123", ["Password must contain at least one special character"]),
        ]
        
        for password, expected_issues in weak_passwords:
            is_valid, issues = SecurityValidator.validate_password_strength(password)
            assert is_valid is False
            for expected_issue in expected_issues:
                assert expected_issue in issues
    
    def test_email_format_validation(self):
        """Test email format validation."""
        # Valid emails
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]
        
        for email in valid_emails:
            assert SecurityValidator.validate_email_format(email) is True
        
        # Invalid emails
        invalid_emails = [
            "invalid",
            "@example.com",
            "test@",
            "test.example.com",
            "test@.com",
            "test@example.",
        ]
        
        for email in invalid_emails:
            assert SecurityValidator.validate_email_format(email) is False


class TestPermissionsAndRoles:
    """Test permissions and role hierarchy."""
    
    def test_check_permissions(self):
        """Test permission checking."""
        from app.core.security import check_permissions
        
        user_permissions = ["read:own", "create:document", "update:own"]
        
        # User has required permissions
        assert check_permissions(user_permissions, ["read:own"]) is True
        assert check_permissions(user_permissions, ["read:own", "create:document"]) is True
        
        # User missing permissions
        assert check_permissions(user_permissions, ["delete:any"]) is False
        assert check_permissions(user_permissions, ["read:own", "admin:users"]) is False
        
        # Empty requirements should always pass
        assert check_permissions(user_permissions, []) is True
    
    def test_role_hierarchy(self):
        """Test role hierarchy checking."""
        from app.core.security import check_role_hierarchy
        
        # Same role
        assert check_role_hierarchy("user", "user") is True
        
        # Higher role
        assert check_role_hierarchy("admin", "user") is True
        assert check_role_hierarchy("superuser", "admin") is True
        assert check_role_hierarchy("superuser", "user") is True
        
        # Lower role
        assert check_role_hierarchy("user", "admin") is False
        assert check_role_hierarchy("moderator", "admin") is False
        
        # Invalid roles
        assert check_role_hierarchy("invalid", "user") is False
        assert check_role_hierarchy("user", "invalid") is False
