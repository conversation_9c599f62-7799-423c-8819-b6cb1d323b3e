"""FastAPI dependencies for authentication and authorization."""

from typing import Optional

import structlog
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_db
from app.core.exceptions import AuthenticationError, AuthorizationError
from app.core.security import verify_token
from app.models.user import User

logger = structlog.get_logger(__name__)

# Security scheme for JWT tokens
security = HTTPBearer(auto_error=False)


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db),
) -> User:
    """
    Get current authenticated user from JWT token.
    
    Args:
        credentials: HTTP authorization credentials
        db: Database session
    
    Returns:
        User: Current authenticated user
    
    Raises:
        HTTPException: If authentication fails
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication credentials required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # Verify JWT token
        payload = verify_token(credentials.credentials)
        user_id = payload.get("sub")
        
        if not user_id:
            raise AuthenticationError("Invalid token payload")
        
        # Get user from database
        result = await db.execute(select(User).where(User.id == int(user_id)))
        user = result.scalar_one_or_none()
        
        if not user:
            raise AuthenticationError("User not found")
        
        if not user.is_active:
            raise AuthenticationError("User account is disabled")
        
        logger.debug("User authenticated successfully", user_id=user.id, username=user.username)
        return user
        
    except AuthenticationError as e:
        logger.warning("Authentication failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error("Authentication error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Get current active user (alias for get_current_user).
    
    Args:
        current_user: Current authenticated user
    
    Returns:
        User: Current active user
    """
    return current_user


async def get_current_admin_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Get current user with admin privileges.
    
    Args:
        current_user: Current authenticated user
    
    Returns:
        User: Current admin user
    
    Raises:
        HTTPException: If user doesn't have admin privileges
    """
    if not current_user.is_admin:
        logger.warning(
            "Admin access denied", 
            user_id=current_user.id, 
            role=current_user.role
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required",
        )
    
    return current_user


async def get_current_superuser(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Get current superuser.
    
    Args:
        current_user: Current authenticated user
    
    Returns:
        User: Current superuser
    
    Raises:
        HTTPException: If user is not a superuser
    """
    if not current_user.is_superuser:
        logger.warning(
            "Superuser access denied", 
            user_id=current_user.id, 
            role=current_user.role
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Superuser privileges required",
        )
    
    return current_user


def require_permissions(*required_permissions: str):
    """
    Dependency factory for permission-based access control.
    
    Args:
        *required_permissions: Required permissions
    
    Returns:
        Dependency function
    """
    async def permission_checker(
        current_user: User = Depends(get_current_user),
    ) -> User:
        """Check if user has required permissions."""
        user_permissions = current_user.permissions
        
        # Superuser has all permissions
        if "*" in user_permissions:
            return current_user
        
        # Check specific permissions
        missing_permissions = []
        for permission in required_permissions:
            if permission not in user_permissions:
                missing_permissions.append(permission)
        
        if missing_permissions:
            logger.warning(
                "Permission denied",
                user_id=current_user.id,
                required_permissions=list(required_permissions),
                missing_permissions=missing_permissions,
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing permissions: {', '.join(missing_permissions)}",
            )
        
        return current_user
    
    return permission_checker


def require_role(required_role: str):
    """
    Dependency factory for role-based access control.
    
    Args:
        required_role: Required role
    
    Returns:
        Dependency function
    """
    async def role_checker(
        current_user: User = Depends(get_current_user),
    ) -> User:
        """Check if user has required role."""
        from app.core.security import check_role_hierarchy
        
        if not check_role_hierarchy(current_user.role.value, required_role):
            logger.warning(
                "Role access denied",
                user_id=current_user.id,
                user_role=current_user.role.value,
                required_role=required_role,
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Role '{required_role}' or higher required",
            )
        
        return current_user
    
    return role_checker


async def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db),
) -> Optional[User]:
    """
    Get current user if authenticated, otherwise return None.
    
    Args:
        credentials: HTTP authorization credentials
        db: Database session
    
    Returns:
        Optional[User]: Current user if authenticated, None otherwise
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials, db)
    except HTTPException:
        return None
