"""External LLM integration service for enhanced chat responses."""

import asyncio
import json
from typing import Dict, List, Optional, Union

import openai
import anthropic
import tiktoken
import structlog

from app.core.config import get_settings
from app.core.exceptions import ProcessingError

settings = get_settings()
logger = structlog.get_logger(__name__)


class LLMService:
    """Service for integrating with external LLM providers."""
    
    def __init__(self):
        """Initialize LLM service with API clients."""
        self.openai_client = None
        self.anthropic_client = None
        self.tokenizer = None
        self._initialize_clients()
    
    def _initialize_clients(self) -> None:
        """Initialize LLM API clients."""
        try:
            # Initialize OpenAI client
            if settings.OPENAI_API_KEY:
                self.openai_client = openai.AsyncOpenAI(
                    api_key=settings.OPENAI_API_KEY
                )
                logger.info("OpenAI client initialized")
            
            # Initialize Anthropic client
            if settings.ANTHROPIC_API_KEY:
                self.anthropic_client = anthropic.AsyncAnthropic(
                    api_key=settings.ANTHROPIC_API_KEY
                )
                logger.info("Anthropic client initialized")
            
            # Initialize tokenizer for token counting
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
            
            if not self.openai_client and not self.anthropic_client:
                logger.warning("No LLM API keys configured, external LLM features disabled")
                
        except Exception as e:
            logger.error("Failed to initialize LLM clients", error=str(e))
    
    async def generate_chat_response(
        self,
        query: str,
        context: List[Dict],
        model: str = "auto",
        max_tokens: int = 1000,
        temperature: float = 0.7
    ) -> Dict:
        """
        Generate enhanced chat response using external LLM.
        
        Args:
            query: User query
            context: RAG context chunks
            model: LLM model to use ('gpt-4', 'claude-3', 'auto')
            max_tokens: Maximum response tokens
            temperature: Response creativity (0.0-1.0)
            
        Returns:
            Dict: Enhanced response with metadata
        """
        logger.info(
            "Generating LLM chat response",
            model=model,
            context_chunks=len(context),
            query_length=len(query)
        )
        
        try:
            # Auto-select model if not specified
            if model == "auto":
                model = await self._select_best_model(query, context)
            
            # Prepare context and prompt
            formatted_context = self._format_context(context)
            prompt = self._create_chat_prompt(query, formatted_context)
            
            # Generate response based on model
            if model.startswith("gpt"):
                response_data = await self._generate_openai_response(
                    prompt, model, max_tokens, temperature
                )
            elif model.startswith("claude"):
                response_data = await self._generate_anthropic_response(
                    prompt, model, max_tokens, temperature
                )
            else:
                raise ProcessingError(f"Unsupported model: {model}")
            
            # Add metadata
            response_data.update({
                "model_used": model,
                "context_chunks": len(context),
                "query": query,
                "token_usage": response_data.get("token_usage", {}),
                "processing_time": response_data.get("processing_time", 0)
            })
            
            logger.info(
                "LLM response generated successfully",
                model=model,
                response_length=len(response_data["response"]),
                tokens_used=response_data.get("token_usage", {}).get("total_tokens", 0)
            )
            
            return response_data
            
        except Exception as e:
            logger.error("LLM response generation failed", error=str(e))
            raise ProcessingError(f"LLM response generation failed: {str(e)}")
    
    async def _select_best_model(self, query: str, context: List[Dict]) -> str:
        """Auto-select the best model based on query and context."""
        # Simple heuristics for model selection
        query_length = len(query)
        context_length = sum(len(chunk.get("content", "")) for chunk in context)
        total_length = query_length + context_length
        
        # Prefer Claude for longer contexts and complex reasoning
        if total_length > 8000 or any(
            keyword in query.lower() 
            for keyword in ["analyze", "compare", "explain", "reasoning", "complex"]
        ):
            if self.anthropic_client:
                return "claude-3-sonnet-20240229"
        
        # Prefer GPT-4 for general queries
        if self.openai_client:
            return "gpt-4-turbo-preview"
        
        # Fallback
        if self.anthropic_client:
            return "claude-3-sonnet-20240229"
        elif self.openai_client:
            return "gpt-3.5-turbo"
        
        raise ProcessingError("No LLM models available")
    
    def _format_context(self, context: List[Dict]) -> str:
        """Format RAG context for LLM prompt."""
        if not context:
            return "No relevant context found."
        
        formatted_chunks = []
        for i, chunk in enumerate(context, 1):
            content = chunk.get("content", "")
            source = chunk.get("source", "Unknown")
            similarity = chunk.get("similarity_score", 0.0)
            
            formatted_chunks.append(
                f"[Context {i}] (Source: {source}, Relevance: {similarity:.2f})\n{content}"
            )
        
        return "\n\n".join(formatted_chunks)
    
    def _create_chat_prompt(self, query: str, context: str) -> str:
        """Create optimized prompt for chat response."""
        return f"""You are an intelligent document assistant. Your task is to provide helpful, accurate, and contextual responses based on the provided document context.

Context Information:
{context}

User Query: {query}

Instructions:
1. Answer the user's query based primarily on the provided context
2. If the context doesn't contain enough information, clearly state this limitation
3. Provide specific references to the source documents when possible
4. Be concise but comprehensive in your response
5. If you need to make inferences, clearly indicate when you're doing so
6. Maintain a helpful and professional tone

Response:"""
    
    async def _generate_openai_response(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> Dict:
        """Generate response using OpenAI API."""
        if not self.openai_client:
            raise ProcessingError("OpenAI client not initialized")
        
        try:
            import time
            start_time = time.time()
            
            response = await self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "You are a helpful document assistant."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature,
                stream=False
            )
            
            processing_time = time.time() - start_time
            
            return {
                "response": response.choices[0].message.content,
                "model": model,
                "token_usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "processing_time": processing_time,
                "provider": "openai"
            }
            
        except Exception as e:
            logger.error("OpenAI API call failed", error=str(e))
            raise ProcessingError(f"OpenAI API call failed: {str(e)}")
    
    async def _generate_anthropic_response(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> Dict:
        """Generate response using Anthropic API."""
        if not self.anthropic_client:
            raise ProcessingError("Anthropic client not initialized")
        
        try:
            import time
            start_time = time.time()
            
            response = await self.anthropic_client.messages.create(
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            processing_time = time.time() - start_time
            
            return {
                "response": response.content[0].text,
                "model": model,
                "token_usage": {
                    "input_tokens": response.usage.input_tokens,
                    "output_tokens": response.usage.output_tokens,
                    "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                },
                "processing_time": processing_time,
                "provider": "anthropic"
            }
            
        except Exception as e:
            logger.error("Anthropic API call failed", error=str(e))
            raise ProcessingError(f"Anthropic API call failed: {str(e)}")
    
    async def summarize_with_llm(
        self,
        text: str,
        style: str = "concise",
        max_length: int = 500
    ) -> Dict:
        """
        Generate high-quality summary using external LLM.
        
        Args:
            text: Text to summarize
            style: Summary style ('concise', 'detailed', 'bullet-points')
            max_length: Maximum summary length
            
        Returns:
            Dict: Summary with metadata
        """
        logger.info("Generating LLM summary", style=style, text_length=len(text))
        
        try:
            # Create summarization prompt
            prompt = self._create_summary_prompt(text, style, max_length)
            
            # Select appropriate model
            model = "gpt-4-turbo-preview" if self.openai_client else "claude-3-sonnet-20240229"
            
            # Generate summary
            if model.startswith("gpt"):
                result = await self._generate_openai_response(
                    prompt, model, max_tokens=max_length//2, temperature=0.3
                )
            else:
                result = await self._generate_anthropic_response(
                    prompt, model, max_tokens=max_length//2, temperature=0.3
                )
            
            return {
                "summary": result["response"],
                "style": style,
                "model_used": model,
                "original_length": len(text),
                "summary_length": len(result["response"]),
                "compression_ratio": len(result["response"]) / len(text),
                "token_usage": result.get("token_usage", {}),
                "method": "llm_generated"
            }
            
        except Exception as e:
            logger.error("LLM summarization failed", error=str(e))
            raise ProcessingError(f"LLM summarization failed: {str(e)}")
    
    def _create_summary_prompt(self, text: str, style: str, max_length: int) -> str:
        """Create optimized prompt for summarization."""
        style_instructions = {
            "concise": "Create a concise, focused summary that captures the main points.",
            "detailed": "Create a comprehensive summary that covers all important aspects.",
            "bullet-points": "Create a summary using bullet points for easy reading."
        }
        
        instruction = style_instructions.get(style, style_instructions["concise"])
        
        return f"""Please summarize the following text. {instruction}

Requirements:
- Maximum length: approximately {max_length} characters
- Focus on the most important information
- Maintain accuracy and clarity
- Use clear, professional language

Text to summarize:
{text}

Summary:"""
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken."""
        if not self.tokenizer:
            # Rough estimation if tokenizer not available
            return len(text.split()) * 1.3
        
        try:
            return len(self.tokenizer.encode(text))
        except Exception:
            return len(text.split()) * 1.3
    
    async def get_available_models(self) -> Dict:
        """Get list of available models from configured providers."""
        models = {
            "openai": [],
            "anthropic": []
        }
        
        if self.openai_client:
            models["openai"] = [
                "gpt-4-turbo-preview",
                "gpt-4",
                "gpt-3.5-turbo",
                "gpt-3.5-turbo-16k"
            ]
        
        if self.anthropic_client:
            models["anthropic"] = [
                "claude-3-opus-20240229",
                "claude-3-sonnet-20240229",
                "claude-3-haiku-20240307"
            ]
        
        return models
    
    async def health_check(self) -> Dict:
        """Check health of LLM service."""
        health_status = {
            "status": "healthy",
            "providers": {}
        }
        
        # Check OpenAI
        if self.openai_client:
            try:
                # Simple test call
                test_response = await self.openai_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": "Hello"}],
                    max_tokens=5
                )
                health_status["providers"]["openai"] = "healthy"
            except Exception as e:
                health_status["providers"]["openai"] = f"unhealthy: {str(e)}"
                health_status["status"] = "degraded"
        else:
            health_status["providers"]["openai"] = "not_configured"
        
        # Check Anthropic
        if self.anthropic_client:
            try:
                # Simple test call
                test_response = await self.anthropic_client.messages.create(
                    model="claude-3-haiku-20240307",
                    max_tokens=5,
                    messages=[{"role": "user", "content": "Hello"}]
                )
                health_status["providers"]["anthropic"] = "healthy"
            except Exception as e:
                health_status["providers"]["anthropic"] = f"unhealthy: {str(e)}"
                health_status["status"] = "degraded"
        else:
            health_status["providers"]["anthropic"] = "not_configured"
        
        # Overall status
        if all(status == "not_configured" for status in health_status["providers"].values()):
            health_status["status"] = "not_configured"
        
        return health_status


# Global instance
llm_service = LLMService()
