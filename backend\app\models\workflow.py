"""Workflow-related database models."""

import enum
from datetime import datetime
from typing import Optional

from sqlalchemy import DateT<PERSON>, Enum, Foreign<PERSON>ey, Integer, JSON, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base


class WorkflowStatusEnum(str, enum.Enum):
    """Workflow status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    ARCHIVED = "archived"


class WorkflowExecutionStatusEnum(str, enum.Enum):
    """Workflow execution status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Workflow(Base):
    """Workflow model for agent orchestration."""
    
    __tablename__ = "workflows"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Basic information
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    
    # Workflow definition
    definition: Mapped[dict] = mapped_column(JSON, nullable=False)  # LangGraph workflow definition
    
    # Status
    status: Mapped[WorkflowStatusEnum] = mapped_column(
        Enum(WorkflowStatusEnum), 
        default=WorkflowStatusEnum.DRAFT, 
        nullable=False
    )
    
    # Ownership
    owner_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    
    # Configuration
    configuration: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships
    owner: Mapped["User"] = relationship("User", back_populates="workflows")
    executions: Mapped[list["WorkflowExecution"]] = relationship(
        "WorkflowExecution", 
        back_populates="workflow",
        cascade="all, delete-orphan"
    )
    steps: Mapped[list["WorkflowStep"]] = relationship(
        "WorkflowStep", 
        back_populates="workflow",
        cascade="all, delete-orphan"
    )


class WorkflowExecution(Base):
    """Workflow execution model for tracking workflow runs."""
    
    __tablename__ = "workflow_executions"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Workflow reference
    workflow_id: Mapped[int] = mapped_column(ForeignKey("workflows.id"), nullable=False)
    
    # Execution details
    input_data: Mapped[dict] = mapped_column(JSON, nullable=False)
    output_data: Mapped[Optional[dict]] = mapped_column(JSON)
    error_message: Mapped[Optional[str]] = mapped_column(Text)
    
    # Status
    status: Mapped[WorkflowExecutionStatusEnum] = mapped_column(
        Enum(WorkflowExecutionStatusEnum), 
        default=WorkflowExecutionStatusEnum.PENDING, 
        nullable=False
    )
    
    # Progress tracking
    current_step: Mapped[Optional[str]] = mapped_column(String(255))
    total_steps: Mapped[Optional[int]] = mapped_column(Integer)
    completed_steps: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # Timestamps
    started_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Relationships
    workflow: Mapped["Workflow"] = relationship("Workflow", back_populates="executions")


class WorkflowStep(Base):
    """Workflow step model for individual workflow steps."""
    
    __tablename__ = "workflow_steps"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Workflow reference
    workflow_id: Mapped[int] = mapped_column(ForeignKey("workflows.id"), nullable=False)
    
    # Step details
    step_name: Mapped[str] = mapped_column(String(255), nullable=False)
    step_type: Mapped[str] = mapped_column(String(100), nullable=False)  # agent, condition, action, etc.
    step_order: Mapped[int] = mapped_column(Integer, nullable=False)
    
    # Step configuration
    configuration: Mapped[dict] = mapped_column(JSON, nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships
    workflow: Mapped["Workflow"] = relationship("Workflow", back_populates="steps")
