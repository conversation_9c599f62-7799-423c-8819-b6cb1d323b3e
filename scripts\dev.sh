#!/bin/bash

# Lonors AI Agent Platform - Development Server Script
# This script starts the development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the project root
if [ ! -f "docker-compose.yml" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Function to check if a service is running
check_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for $service_name to be ready on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "http://localhost:$port" > /dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_error "$service_name failed to start after $max_attempts attempts"
            return 1
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
}

# Function to start database services
start_databases() {
    print_status "Starting database services..."
    
    docker-compose up -d postgres qdrant dragonfly neo4j
    
    # Wait for databases to be ready
    print_status "Waiting for databases to be ready..."
    sleep 10
    
    # Check PostgreSQL
    until docker-compose exec -T postgres pg_isready -U lonors > /dev/null 2>&1; do
        print_status "Waiting for PostgreSQL..."
        sleep 2
    done
    print_success "PostgreSQL is ready!"
    
    # Check Qdrant
    until curl -f -s http://localhost:6333/health > /dev/null 2>&1; do
        print_status "Waiting for Qdrant..."
        sleep 2
    done
    print_success "Qdrant is ready!"
    
    # Check DragonflyDB
    until docker-compose exec -T dragonfly redis-cli ping > /dev/null 2>&1; do
        print_status "Waiting for DragonflyDB..."
        sleep 2
    done
    print_success "DragonflyDB is ready!"
    
    print_success "All database services are ready!"
}

# Function to run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    cd backend
    if uv run alembic upgrade head; then
        print_success "Database migrations completed successfully"
    else
        print_error "Database migrations failed"
        exit 1
    fi
    cd ..
}

# Function to start backend
start_backend() {
    print_status "Starting backend server..."
    
    cd backend
    
    # Install dependencies if needed
    if [ ! -d ".venv" ]; then
        print_status "Installing backend dependencies..."
        uv sync --dev
    fi
    
    # Start backend in background
    uv run uvicorn app.main:app --host 0.0.0.0 --port 3000 --reload &
    BACKEND_PID=$!
    
    cd ..
    
    # Wait for backend to be ready
    if check_service "Backend API" 3000; then
        print_success "Backend server started successfully (PID: $BACKEND_PID)"
    else
        print_error "Backend server failed to start"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
}

# Function to start frontend
start_frontend() {
    print_status "Starting frontend server..."
    
    cd frontend
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies..."
        pnpm install
    fi
    
    # Start frontend in background
    pnpm dev &
    FRONTEND_PID=$!
    
    cd ..
    
    # Wait for frontend to be ready
    if check_service "Frontend" 5500; then
        print_success "Frontend server started successfully (PID: $FRONTEND_PID)"
    else
        print_error "Frontend server failed to start"
        kill $FRONTEND_PID 2>/dev/null || true
        exit 1
    fi
}

# Function to show status
show_status() {
    echo
    print_success "🎉 Lonors AI Agent Platform is running!"
    echo
    echo "Services:"
    echo "  📊 Frontend:        http://localhost:5500"
    echo "  🔧 Backend API:     http://localhost:3000"
    echo "  📚 API Docs:        http://localhost:3000/docs"
    echo "  🗄️  PostgreSQL:     localhost:5432"
    echo "  🔍 Qdrant:          http://localhost:6333"
    echo "  ⚡ DragonflyDB:     localhost:6379"
    echo "  🕸️  Neo4j:           http://localhost:7474"
    echo "  📈 Prometheus:      http://localhost:9090"
    echo "  📊 Grafana:         http://localhost:3001"
    echo
    echo "Logs:"
    echo "  Backend:  tail -f backend/logs/app.log"
    echo "  Frontend: Check the terminal where you ran this script"
    echo
    echo "To stop all services: ./scripts/stop.sh"
    echo "To run tests: ./scripts/test.sh"
    echo
}

# Function to handle cleanup on exit
cleanup() {
    print_status "Shutting down development servers..."
    
    # Kill background processes
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    print_success "Development servers stopped"
}

# Set up signal handlers
trap cleanup EXIT INT TERM

# Parse command line arguments
SKIP_DB=false
SKIP_MIGRATIONS=false
BACKEND_ONLY=false
FRONTEND_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-db)
            SKIP_DB=true
            shift
            ;;
        --skip-migrations)
            SKIP_MIGRATIONS=true
            shift
            ;;
        --backend-only)
            BACKEND_ONLY=true
            shift
            ;;
        --frontend-only)
            FRONTEND_ONLY=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --skip-db           Skip starting database services"
            echo "  --skip-migrations   Skip running database migrations"
            echo "  --backend-only      Start only the backend server"
            echo "  --frontend-only     Start only the frontend server"
            echo "  -h, --help          Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Main execution
echo
print_status "Starting Lonors AI Agent Platform Development Environment"
echo "========================================================"

# Start database services
if [ "$SKIP_DB" = false ] && [ "$FRONTEND_ONLY" = false ]; then
    start_databases
fi

# Run migrations
if [ "$SKIP_MIGRATIONS" = false ] && [ "$FRONTEND_ONLY" = false ]; then
    run_migrations
fi

# Start backend
if [ "$FRONTEND_ONLY" = false ]; then
    start_backend
fi

# Start frontend
if [ "$BACKEND_ONLY" = false ]; then
    start_frontend
fi

# Show status
show_status

# Keep script running
print_status "Press Ctrl+C to stop all services"
wait
