"""Document management endpoints."""

from typing import List, Optional

import structlog
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies import get_current_user
from app.core.database import get_db
from app.models.document import Document
from app.models.user import User

logger = structlog.get_logger(__name__)

router = APIRouter()


@router.get("/", response_model=List[dict])
async def list_documents(
    skip: int = Query(0, ge=0, description="Number of documents to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of documents to return"),
    search: Optional[str] = Query(None, description="Search documents by title"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> List[dict]:
    """
    List user's documents with pagination and search.
    
    Args:
        skip: Number of documents to skip
        limit: Number of documents to return
        search: Search term for document title
        current_user: Current authenticated user
        db: Database session
    
    Returns:
        List[dict]: List of documents
    """
    logger.info(
        "Listing documents",
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        search=search,
    )
    
    # Build query for user's documents
    query = select(Document).where(Document.owner_id == current_user.id)
    
    # Add search filter
    if search:
        search_term = f"%{search}%"
        query = query.where(Document.title.ilike(search_term))
    
    # Add pagination
    query = query.offset(skip).limit(limit).order_by(Document.created_at.desc())
    
    # Execute query
    result = await db.execute(query)
    documents = result.scalars().all()
    
    logger.info("Documents listed successfully", count=len(documents))
    
    # Convert to dict (temporary until we have proper schemas)
    return [
        {
            "id": doc.id,
            "title": doc.title,
            "filename": doc.filename,
            "document_type": doc.document_type.value,
            "status": doc.status.value,
            "file_size": doc.file_size,
            "file_size_mb": doc.file_size_mb,
            "chunk_count": doc.chunk_count,
            "created_at": doc.created_at.isoformat(),
            "updated_at": doc.updated_at.isoformat(),
            "processed_at": doc.processed_at.isoformat() if doc.processed_at else None,
        }
        for doc in documents
    ]


@router.get("/{document_id}", response_model=dict)
async def get_document(
    document_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> dict:
    """
    Get document by ID.
    
    Args:
        document_id: Document ID
        current_user: Current authenticated user
        db: Database session
    
    Returns:
        dict: Document information
    
    Raises:
        HTTPException: If document not found or access denied
    """
    logger.info("Getting document", document_id=document_id, user_id=current_user.id)
    
    # Get document
    result = await db.execute(
        select(Document).where(
            (Document.id == document_id) & (Document.owner_id == current_user.id)
        )
    )
    document = result.scalar_one_or_none()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    logger.info("Document retrieved successfully", document_id=document_id)
    
    return {
        "id": document.id,
        "title": document.title,
        "filename": document.filename,
        "file_path": document.file_path,
        "document_type": document.document_type.value,
        "mime_type": document.mime_type,
        "status": document.status.value,
        "file_size": document.file_size,
        "file_size_mb": document.file_size_mb,
        "content_hash": document.content_hash,
        "language": document.language,
        "tags": document.tags,
        "custom_metadata": document.custom_metadata,
        "embedding_model": document.embedding_model,
        "embedding_dimensions": document.embedding_dimensions,
        "chunk_count": document.chunk_count,
        "created_at": document.created_at.isoformat(),
        "updated_at": document.updated_at.isoformat(),
        "processed_at": document.processed_at.isoformat() if document.processed_at else None,
        "processing_error": document.processing_error,
    }


@router.delete("/{document_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_document(
    document_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> None:
    """
    Delete document.
    
    Args:
        document_id: Document ID
        current_user: Current authenticated user
        db: Database session
    
    Raises:
        HTTPException: If document not found or access denied
    """
    logger.info("Deleting document", document_id=document_id, user_id=current_user.id)
    
    # Get document
    result = await db.execute(
        select(Document).where(
            (Document.id == document_id) & (Document.owner_id == current_user.id)
        )
    )
    document = result.scalar_one_or_none()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    try:
        # Delete document (this will cascade to chunks and metadata)
        await db.delete(document)
        await db.commit()
        
        logger.info("Document deleted successfully", document_id=document_id)
        
        # TODO: Delete actual file from storage
        # TODO: Delete vectors from vector database
        
    except Exception as e:
        logger.error("Document deletion error", error=str(e), document_id=document_id)
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Document deletion failed"
        )
