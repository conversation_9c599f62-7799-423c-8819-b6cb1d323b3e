"""Document management endpoints."""

import asyncio
from typing import List, Optional

import structlog
from fastapi import APIRouter, Depends, File, Form, HTTPException, Query, UploadFile, status
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies import get_current_user
from app.core.database import get_db
from app.core.exceptions import ProcessingError, ValidationError
from app.models.document import Document, DocumentStatusEnum
from app.models.user import User
from app.schemas.document import (
    Document as DocumentSchema,
    DocumentProcessingStatus,
    DocumentPublic,
    DocumentSearchQuery,
    DocumentSearchResult,
    DocumentStats,
    DocumentUploadResponse,
)
from app.services.document_processor import DocumentProcessor
from app.services.rag_service import rag_service

logger = structlog.get_logger(__name__)

router = APIRouter()

# Initialize document processor
document_processor = DocumentProcessor()


@router.post("/upload", response_model=DocumentUploadResponse, status_code=status.HTTP_201_CREATED)
async def upload_document(
    file: UploadFile = File(..., description="Document file to upload"),
    title: Optional[str] = Form(None, description="Document title (optional)"),
    tags: Optional[str] = Form(None, description="Comma-separated tags (optional)"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> DocumentUploadResponse:
    """
    Upload a document for processing.

    Args:
        file: Uploaded file
        title: Optional document title
        tags: Optional comma-separated tags
        current_user: Current authenticated user
        db: Database session

    Returns:
        DocumentUploadResponse: Upload response with document info

    Raises:
        HTTPException: If upload fails
    """
    logger.info(
        "Document upload started",
        filename=file.filename,
        content_type=file.content_type,
        user_id=current_user.id,
    )

    try:
        # Read file content
        file_content = await file.read()

        # Validate file
        doc_type, mime_type = await document_processor.validate_file(
            file_content, file.filename
        )

        # Save file to storage
        file_path, content_hash = await document_processor.save_file(
            file_content, file.filename, current_user.id
        )

        # Parse tags
        tag_list = None
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]

        # Create document record
        document = Document(
            title=title or file.filename,
            filename=file.filename,
            file_path=file_path,
            file_size=len(file_content),
            document_type=doc_type,
            mime_type=mime_type,
            content_hash=content_hash,
            status=DocumentStatusEnum.UPLOADED,
            owner_id=current_user.id,
            tags=tag_list,
        )

        db.add(document)
        await db.commit()
        await db.refresh(document)

        # Start background processing
        asyncio.create_task(process_document_background(document.id))

        logger.info(
            "Document uploaded successfully",
            document_id=document.id,
            filename=file.filename,
            user_id=current_user.id,
        )

        return DocumentUploadResponse.from_orm(document)

    except (ValidationError, ProcessingError) as e:
        logger.warning("Document upload failed", error=str(e), filename=file.filename)
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error("Document upload error", error=str(e), filename=file.filename)
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Document upload failed"
        )


async def process_document_background(document_id: int) -> None:
    """
    Background task to process uploaded document.

    Args:
        document_id: Document ID to process
    """
    from app.core.database import AsyncSessionLocal

    async with AsyncSessionLocal() as db:
        try:
            # Get document
            result = await db.execute(select(Document).where(Document.id == document_id))
            document = result.scalar_one_or_none()

            if not document:
                logger.error("Document not found for processing", document_id=document_id)
                return

            # Update status to processing
            document.status = DocumentStatusEnum.PROCESSING
            await db.commit()

            logger.info("Starting document processing", document_id=document_id)

            # Extract text
            text_content = await document_processor.extract_text(
                document.file_path, document.document_type
            )

            # Update document with content
            document.content = text_content

            # Create chunks
            chunks_data = document_processor.create_chunks(
                text_content,
                metadata={
                    "document_id": document.id,
                    "document_title": document.title,
                    "document_type": document.document_type.value,
                }
            )

            # Save chunks to database
            from app.models.document import DocumentChunk
            import hashlib

            for chunk_data in chunks_data:
                chunk_hash = hashlib.sha256(chunk_data["content"].encode()).hexdigest()

                chunk = DocumentChunk(
                    document_id=document.id,
                    chunk_index=chunk_data["chunk_index"],
                    content=chunk_data["content"],
                    content_hash=chunk_hash,
                    start_position=chunk_data["start_position"],
                    end_position=chunk_data["end_position"],
                    character_count=chunk_data["character_count"],
                    token_count=chunk_data["token_count"],
                )

                db.add(chunk)

            # Update document status
            document.status = DocumentStatusEnum.PROCESSED
            document.chunk_count = len(chunks_data)
            document.processed_at = func.now()

            await db.commit()
            await db.refresh(document)

            # Process chunks for embeddings
            try:
                await rag_service.initialize()

                # Prepare chunk data for embedding processing
                chunk_records = []
                for chunk_data in chunks_data:
                    chunk_records.append({
                        "id": None,  # Will be set after commit
                        "content": chunk_data["content"],
                        "chunk_index": chunk_data["chunk_index"],
                        "character_count": chunk_data["character_count"],
                        "token_count": chunk_data["token_count"],
                        "start_position": chunk_data["start_position"],
                        "end_position": chunk_data["end_position"],
                        "document_title": document.title,
                        "document_type": document.document_type.value,
                    })

                # Get actual chunk IDs after commit
                result = await db.execute(
                    select(DocumentChunk)
                    .where(DocumentChunk.document_id == document.id)
                    .order_by(DocumentChunk.chunk_index)
                )
                saved_chunks = result.scalars().all()

                # Update chunk records with actual IDs
                for i, chunk_record in enumerate(chunk_records):
                    if i < len(saved_chunks):
                        chunk_record["id"] = saved_chunks[i].id

                # Process embeddings
                await rag_service.process_document_chunks(document.id, chunk_records)

                logger.info(
                    "Document embeddings processed",
                    document_id=document_id,
                    chunk_count=len(chunk_records),
                )

            except Exception as e:
                logger.error(
                    "Failed to process embeddings",
                    document_id=document_id,
                    error=str(e),
                )
                # Don't fail the entire process if embeddings fail

            logger.info(
                "Document processing completed",
                document_id=document_id,
                chunk_count=len(chunks_data),
            )

        except Exception as e:
            logger.error(
                "Document processing failed",
                document_id=document_id,
                error=str(e),
            )

            # Update document status to failed
            try:
                document.status = DocumentStatusEnum.FAILED
                document.processing_error = str(e)
                await db.commit()
            except Exception:
                pass  # Don't fail if we can't update status


@router.get("/", response_model=List[DocumentPublic])
async def list_documents(
    skip: int = Query(0, ge=0, description="Number of documents to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of documents to return"),
    search: Optional[str] = Query(None, description="Search documents by title"),
    status: Optional[DocumentStatusEnum] = Query(None, description="Filter by status"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> List[DocumentPublic]:
    """
    List user's documents with pagination and search.

    Args:
        skip: Number of documents to skip
        limit: Number of documents to return
        search: Search term for document title
        status: Filter by document status
        current_user: Current authenticated user
        db: Database session

    Returns:
        List[DocumentPublic]: List of documents
    """
    logger.info(
        "Listing documents",
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        search=search,
        status=status,
    )

    # Build query for user's documents
    query = select(Document).where(Document.owner_id == current_user.id)

    # Add search filter
    if search:
        search_term = f"%{search}%"
        query = query.where(Document.title.ilike(search_term))

    # Add status filter
    if status:
        query = query.where(Document.status == status)

    # Add pagination
    query = query.offset(skip).limit(limit).order_by(Document.created_at.desc())

    # Execute query
    result = await db.execute(query)
    documents = result.scalars().all()

    logger.info("Documents listed successfully", count=len(documents))

    return [DocumentPublic.from_orm(doc) for doc in documents]


@router.get("/{document_id}", response_model=DocumentSchema)
async def get_document(
    document_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> DocumentSchema:
    """
    Get document by ID.

    Args:
        document_id: Document ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        DocumentSchema: Document information

    Raises:
        HTTPException: If document not found or access denied
    """
    logger.info("Getting document", document_id=document_id, user_id=current_user.id)

    # Get document
    result = await db.execute(
        select(Document).where(
            (Document.id == document_id) & (Document.owner_id == current_user.id)
        )
    )
    document = result.scalar_one_or_none()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    logger.info("Document retrieved successfully", document_id=document_id)

    return DocumentSchema.from_orm(document)


@router.delete("/{document_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_document(
    document_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> None:
    """
    Delete document.
    
    Args:
        document_id: Document ID
        current_user: Current authenticated user
        db: Database session
    
    Raises:
        HTTPException: If document not found or access denied
    """
    logger.info("Deleting document", document_id=document_id, user_id=current_user.id)
    
    # Get document
    result = await db.execute(
        select(Document).where(
            (Document.id == document_id) & (Document.owner_id == current_user.id)
        )
    )
    document = result.scalar_one_or_none()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    try:
        # Delete vectors from vector database first
        try:
            await rag_service.delete_document_vectors(document_id)
        except Exception as e:
            logger.warning(
                "Failed to delete document vectors",
                document_id=document_id,
                error=str(e),
            )
            # Continue with document deletion even if vector deletion fails

        # Delete document (this will cascade to chunks and metadata)
        await db.delete(document)
        await db.commit()

        logger.info("Document deleted successfully", document_id=document_id)

        # TODO: Delete actual file from storage
        
    except Exception as e:
        logger.error("Document deletion error", error=str(e), document_id=document_id)
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Document deletion failed"
        )


@router.get("/{document_id}/status", response_model=DocumentProcessingStatus)
async def get_document_processing_status(
    document_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> DocumentProcessingStatus:
    """
    Get document processing status.

    Args:
        document_id: Document ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        DocumentProcessingStatus: Processing status information

    Raises:
        HTTPException: If document not found or access denied
    """
    logger.info("Getting document status", document_id=document_id, user_id=current_user.id)

    # Get document
    result = await db.execute(
        select(Document).where(
            (Document.id == document_id) & (Document.owner_id == current_user.id)
        )
    )
    document = result.scalar_one_or_none()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Calculate progress if processing
    progress_percentage = None
    current_step = None

    if document.status == DocumentStatusEnum.PROCESSING:
        current_step = "processing_text"
        progress_percentage = 50.0  # Simplified progress calculation
    elif document.status == DocumentStatusEnum.PROCESSED:
        progress_percentage = 100.0
    elif document.status == DocumentStatusEnum.FAILED:
        progress_percentage = 0.0

    return DocumentProcessingStatus(
        document_id=document.id,
        status=document.status,
        progress_percentage=progress_percentage,
        current_step=current_step,
        error_message=document.processing_error,
        chunks_processed=document.chunk_count,
        total_chunks=document.chunk_count if document.status == DocumentStatusEnum.PROCESSED else None,
    )


@router.get("/stats", response_model=DocumentStats)
async def get_document_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> DocumentStats:
    """
    Get document statistics for current user.

    Args:
        current_user: Current authenticated user
        db: Database session

    Returns:
        DocumentStats: Document statistics
    """
    logger.info("Getting document statistics", user_id=current_user.id)

    # Get total document count
    total_result = await db.execute(
        select(func.count(Document.id)).where(Document.owner_id == current_user.id)
    )
    total_documents = total_result.scalar() or 0

    # Get documents by type
    type_result = await db.execute(
        select(Document.document_type, func.count(Document.id))
        .where(Document.owner_id == current_user.id)
        .group_by(Document.document_type)
    )
    documents_by_type = {doc_type.value: count for doc_type, count in type_result.all()}

    # Get documents by status
    status_result = await db.execute(
        select(Document.status, func.count(Document.id))
        .where(Document.owner_id == current_user.id)
        .group_by(Document.status)
    )
    documents_by_status = {status.value: count for status, count in status_result.all()}

    # Get total size
    size_result = await db.execute(
        select(func.sum(Document.file_size)).where(Document.owner_id == current_user.id)
    )
    total_size_bytes = size_result.scalar() or 0
    total_size_mb = round(total_size_bytes / (1024 * 1024), 2)

    # Get total chunks
    chunks_result = await db.execute(
        select(func.sum(Document.chunk_count)).where(Document.owner_id == current_user.id)
    )
    total_chunks = chunks_result.scalar() or 0

    # Calculate average chunks per document
    average_chunks = round(total_chunks / total_documents, 2) if total_documents > 0 else 0.0

    return DocumentStats(
        total_documents=total_documents,
        documents_by_type=documents_by_type,
        documents_by_status=documents_by_status,
        total_size_mb=total_size_mb,
        total_chunks=total_chunks,
        average_chunks_per_document=average_chunks,
    )


@router.post("/search", response_model=List[DocumentSearchResult])
async def search_documents(
    search_query: DocumentSearchQuery,
    current_user: User = Depends(get_current_user),
) -> List[DocumentSearchResult]:
    """
    Perform semantic search across user's documents.

    Args:
        search_query: Search query parameters
        current_user: Current authenticated user

    Returns:
        List[DocumentSearchResult]: Search results

    Raises:
        HTTPException: If search fails
    """
    logger.info(
        "Performing document search",
        user_id=current_user.id,
        query=search_query.query[:100],
        limit=search_query.limit,
    )

    try:
        # Initialize RAG service if needed
        await rag_service.initialize()

        # Perform semantic search
        search_results = await rag_service.semantic_search(
            query=search_query.query,
            limit=search_query.limit,
            threshold=search_query.threshold,
            document_ids=search_query.document_ids,
            user_id=current_user.id,
        )

        # Convert to response format
        results = []
        for result in search_results:
            search_result = DocumentSearchResult(
                chunk_id=result["chunk_id"],
                document_id=result["document_id"],
                document_title=result["document_title"],
                content=result["content"],
                similarity_score=result["similarity_score"],
                chunk_index=result["chunk_index"],
                page_number=result.get("page_number"),
                start_position=result.get("start_position"),
                end_position=result.get("end_position"),
            )
            results.append(search_result)

        logger.info(
            "Document search completed",
            user_id=current_user.id,
            results_count=len(results),
        )

        return results

    except ProcessingError as e:
        logger.warning("Document search failed", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error("Document search error", error=str(e), user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Document search failed"
        )


@router.post("/chat", response_model=dict)
async def chat_with_documents(
    query: str = Form(..., description="Chat query"),
    context_limit: int = Form(default=5, description="Maximum context chunks"),
    current_user: User = Depends(get_current_user),
) -> dict:
    """
    Chat with documents using RAG.

    Args:
        query: User query
        context_limit: Maximum number of context chunks
        current_user: Current authenticated user

    Returns:
        dict: RAG response with context and sources

    Raises:
        HTTPException: If chat fails
    """
    logger.info(
        "Processing chat query",
        user_id=current_user.id,
        query_length=len(query),
        context_limit=context_limit,
    )

    try:
        # Initialize RAG service if needed
        await rag_service.initialize()

        # Generate RAG response
        response = await rag_service.generate_rag_response(
            query=query,
            context_limit=context_limit,
            user_id=current_user.id,
        )

        logger.info(
            "Chat query processed",
            user_id=current_user.id,
            confidence=response["confidence"],
            sources_count=len(response["sources"]),
        )

        return response

    except ProcessingError as e:
        logger.warning("Chat query failed", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error("Chat query error", error=str(e), user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Chat query failed"
        )
