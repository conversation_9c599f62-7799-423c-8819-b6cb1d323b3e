"""RAG (Retrieval-Augmented Generation) service."""

import asyncio
from typing import Dict, List, Optional, Tuple

import structlog
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import get_settings
from app.core.database import AsyncSessionLocal
from app.core.exceptions import ProcessingError, VectorDatabaseError
from app.models.document import Document, DocumentChunk
from app.services.embedding_service import embedding_service
from app.services.vector_store import vector_store
from app.services.llm_service import llm_service
from app.services.ai_features import ai_features_service

settings = get_settings()
logger = structlog.get_logger(__name__)


class RAGService:
    """Service for Retrieval-Augmented Generation operations."""
    
    def __init__(self):
        """Initialize RAG service."""
        self.embedding_service = embedding_service
        self.vector_store = vector_store
        self.collection_name = settings.QDRANT_COLLECTION_NAME
    
    async def initialize(self) -> None:
        """Initialize RAG service components."""
        logger.info("Initializing RAG service")
        
        # Initialize embedding service
        await self.embedding_service.initialize()
        
        # Initialize vector store
        await self.vector_store.initialize()
        
        # Create collection if it doesn't exist
        await self.vector_store.create_collection(
            dimension=self.embedding_service.dimension
        )
        
        logger.info("RAG service initialized successfully")
    
    async def process_document_chunks(
        self, 
        document_id: int, 
        chunks: List[Dict]
    ) -> None:
        """
        Process document chunks and store embeddings.
        
        Args:
            document_id: Document ID
            chunks: List of chunk data dictionaries
            
        Raises:
            ProcessingError: If processing fails
        """
        if not chunks:
            logger.warning("No chunks to process", document_id=document_id)
            return
        
        logger.info(
            "Processing document chunks for embeddings",
            document_id=document_id,
            chunk_count=len(chunks),
        )
        
        try:
            # Extract text content from chunks
            texts = [chunk["content"] for chunk in chunks]
            
            # Generate embeddings in batches
            embeddings = await self.embedding_service.generate_embeddings_batch(
                texts, batch_size=settings.EMBEDDING_BATCH_SIZE
            )
            
            # Prepare payloads for vector storage
            payloads = []
            vector_ids = []
            
            for i, chunk in enumerate(chunks):
                vector_id = f"doc_{document_id}_chunk_{chunk['chunk_index']}"
                vector_ids.append(vector_id)
                
                payload = {
                    "document_id": document_id,
                    "chunk_id": chunk.get("id"),
                    "chunk_index": chunk["chunk_index"],
                    "content": chunk["content"],
                    "character_count": chunk["character_count"],
                    "token_count": chunk.get("token_count", 0),
                    "start_position": chunk.get("start_position"),
                    "end_position": chunk.get("end_position"),
                    "page_number": chunk.get("page_number"),
                    "document_title": chunk.get("document_title", ""),
                    "document_type": chunk.get("document_type", ""),
                }
                payloads.append(payload)
            
            # Store vectors in Qdrant
            stored_ids = await self.vector_store.add_vectors(
                vectors=embeddings,
                payloads=payloads,
                ids=vector_ids,
            )
            
            # Update chunk records with vector IDs
            await self._update_chunk_vector_ids(chunks, stored_ids)
            
            logger.info(
                "Document chunks processed successfully",
                document_id=document_id,
                vectors_stored=len(stored_ids),
            )
            
        except Exception as e:
            logger.error(
                "Failed to process document chunks",
                document_id=document_id,
                error=str(e),
            )
            raise ProcessingError(f"Failed to process document chunks: {str(e)}")
    
    async def _update_chunk_vector_ids(
        self, 
        chunks: List[Dict], 
        vector_ids: List[str]
    ) -> None:
        """Update chunk records with vector IDs."""
        if len(chunks) != len(vector_ids):
            logger.warning("Mismatch between chunks and vector IDs")
            return
        
        async with AsyncSessionLocal() as db:
            try:
                for chunk, vector_id in zip(chunks, vector_ids):
                    if chunk_id := chunk.get("id"):
                        result = await db.execute(
                            select(DocumentChunk).where(DocumentChunk.id == chunk_id)
                        )
                        chunk_record = result.scalar_one_or_none()
                        
                        if chunk_record:
                            chunk_record.vector_id = vector_id
                            chunk_record.embedding_model = self.embedding_service.model_name
                
                await db.commit()
                logger.debug("Updated chunk vector IDs", count=len(vector_ids))
                
            except Exception as e:
                logger.error("Failed to update chunk vector IDs", error=str(e))
                await db.rollback()
    
    async def semantic_search(
        self,
        query: str,
        limit: int = 10,
        threshold: float = 0.7,
        document_ids: Optional[List[int]] = None,
        user_id: Optional[int] = None,
    ) -> List[Dict]:
        """
        Perform semantic search across document chunks.
        
        Args:
            query: Search query
            limit: Maximum number of results
            threshold: Minimum similarity threshold
            document_ids: Optional list of document IDs to search within
            user_id: Optional user ID for access control
            
        Returns:
            List[Dict]: Search results with metadata
            
        Raises:
            ProcessingError: If search fails
        """
        if not query.strip():
            raise ProcessingError("Query cannot be empty")
        
        logger.info(
            "Performing semantic search",
            query_length=len(query),
            limit=limit,
            threshold=threshold,
            document_ids=document_ids,
        )
        
        try:
            # Generate query embedding
            query_embedding = await self.embedding_service.generate_embedding(query)
            
            # Prepare filter conditions
            filter_conditions = {}
            if document_ids:
                # Note: Qdrant filtering would need to be implemented differently
                # This is a simplified version
                pass
            
            # Search vectors
            search_results = await self.vector_store.search_vectors(
                query_vector=query_embedding,
                limit=limit,
                score_threshold=threshold,
                filter_conditions=filter_conditions,
            )
            
            # Process and enrich results
            enriched_results = await self._enrich_search_results(
                search_results, user_id
            )
            
            logger.info(
                "Semantic search completed",
                query_length=len(query),
                results_count=len(enriched_results),
            )
            
            return enriched_results
            
        except Exception as e:
            logger.error("Semantic search failed", error=str(e), query=query[:100])
            raise ProcessingError(f"Semantic search failed: {str(e)}")
    
    async def _enrich_search_results(
        self, 
        search_results: List[Tuple[str, float, Dict]], 
        user_id: Optional[int] = None
    ) -> List[Dict]:
        """Enrich search results with additional metadata."""
        if not search_results:
            return []
        
        enriched_results = []
        
        async with AsyncSessionLocal() as db:
            for vector_id, score, payload in search_results:
                try:
                    # Get document information
                    document_id = payload.get("document_id")
                    if document_id:
                        result = await db.execute(
                            select(Document).where(Document.id == document_id)
                        )
                        document = result.scalar_one_or_none()
                        
                        # Check user access if user_id provided
                        if user_id and document and document.owner_id != user_id:
                            continue  # Skip documents user doesn't own
                        
                        if document:
                            enriched_result = {
                                "vector_id": vector_id,
                                "similarity_score": score,
                                "chunk_id": payload.get("chunk_id"),
                                "chunk_index": payload.get("chunk_index", 0),
                                "content": payload.get("content", ""),
                                "character_count": payload.get("character_count", 0),
                                "token_count": payload.get("token_count", 0),
                                "start_position": payload.get("start_position"),
                                "end_position": payload.get("end_position"),
                                "page_number": payload.get("page_number"),
                                "document_id": document.id,
                                "document_title": document.title,
                                "document_filename": document.filename,
                                "document_type": document.document_type.value,
                                "document_created_at": document.created_at.isoformat(),
                            }
                            enriched_results.append(enriched_result)
                
                except Exception as e:
                    logger.warning(
                        "Failed to enrich search result",
                        vector_id=vector_id,
                        error=str(e),
                    )
                    continue
        
        return enriched_results
    
    async def generate_rag_response(
        self,
        query: str,
        context_limit: int = 5,
        max_context_length: int = 4000,
        user_id: Optional[int] = None,
    ) -> Dict:
        """
        Generate RAG response with retrieved context.
        
        Args:
            query: User query
            context_limit: Maximum number of context chunks
            max_context_length: Maximum total context length
            user_id: Optional user ID for access control
            
        Returns:
            Dict: RAG response with context and metadata
        """
        logger.info(
            "Generating RAG response",
            query_length=len(query),
            context_limit=context_limit,
        )
        
        try:
            # Perform semantic search
            search_results = await self.semantic_search(
                query=query,
                limit=context_limit,
                threshold=0.6,  # Lower threshold for RAG
                user_id=user_id,
            )
            
            if not search_results:
                return {
                    "query": query,
                    "response": "I couldn't find relevant information to answer your question.",
                    "context": [],
                    "sources": [],
                    "confidence": 0.0,
                }
            
            # Prepare context
            context_chunks = []
            total_length = 0
            sources = set()
            
            for result in search_results:
                content = result["content"]
                if total_length + len(content) <= max_context_length:
                    context_chunks.append({
                        "content": content,
                        "source": f"{result['document_title']} (p. {result.get('page_number', '?')})",
                        "similarity_score": result["similarity_score"],
                    })
                    total_length += len(content)
                    sources.add(result["document_title"])
                else:
                    break
            
            # Generate enhanced response using LLM if available
            try:
                llm_response = await llm_service.generate_chat_response(
                    query=query,
                    context=context_chunks,
                    model=settings.DEFAULT_LLM_MODEL,
                    max_tokens=settings.LLM_MAX_TOKENS,
                    temperature=settings.LLM_TEMPERATURE
                )
                response = llm_response["response"]
                response_metadata = {
                    "model_used": llm_response.get("model_used"),
                    "token_usage": llm_response.get("token_usage", {}),
                    "processing_time": llm_response.get("processing_time", 0)
                }
            except Exception as e:
                logger.warning("LLM response generation failed, using fallback", error=str(e))
                response = self._generate_simple_response(query, context_chunks)
                response_metadata = {"method": "fallback"}

            # Calculate confidence based on similarity scores
            confidence = sum(chunk["similarity_score"] for chunk in context_chunks) / len(context_chunks)
            
            return {
                "query": query,
                "response": response,
                "context": context_chunks,
                "sources": list(sources),
                "confidence": round(confidence, 3),
                "context_length": total_length,
                "response_metadata": response_metadata,
            }
            
        except Exception as e:
            logger.error("RAG response generation failed", error=str(e))
            raise ProcessingError(f"RAG response generation failed: {str(e)}")
    
    def _generate_simple_response(self, query: str, context_chunks: List[Dict]) -> str:
        """Generate a simple response based on context (placeholder for LLM integration)."""
        if not context_chunks:
            return "I couldn't find relevant information to answer your question."
        
        # Simple response generation (would be replaced with LLM call)
        context_text = "\n\n".join(chunk["content"] for chunk in context_chunks)
        
        return (
            f"Based on the available documents, here's what I found:\n\n"
            f"{context_text[:500]}{'...' if len(context_text) > 500 else ''}\n\n"
            f"This information comes from {len(set(chunk['source'] for chunk in context_chunks))} source(s)."
        )

    async def analyze_document(self, document_id: int, text: str) -> Dict:
        """
        Perform comprehensive document analysis using AI features.

        Args:
            document_id: Document ID
            text: Document text content

        Returns:
            Dict: Analysis results including summary, categories, sentiment, etc.
        """
        logger.info("Performing document analysis", document_id=document_id, text_length=len(text))

        try:
            # Run analysis tasks concurrently
            analysis_tasks = [
                ai_features_service.summarize_document(text, max_length=500, style="concise"),
                ai_features_service.categorize_document(text),
                ai_features_service.analyze_document_sentiment(text),
                ai_features_service.extract_entities(text),
            ]

            # Execute all analysis tasks
            summary_result, category_result, sentiment_result, entities_result = await asyncio.gather(
                *analysis_tasks, return_exceptions=True
            )

            # Compile results
            analysis_results = {
                "document_id": document_id,
                "text_length": len(text),
                "analysis_timestamp": asyncio.get_event_loop().time(),
            }

            # Add summary results
            if isinstance(summary_result, dict):
                analysis_results["summary"] = summary_result
            else:
                logger.warning("Summary generation failed", error=str(summary_result))
                analysis_results["summary"] = {"error": str(summary_result)}

            # Add categorization results
            if isinstance(category_result, dict):
                analysis_results["categorization"] = category_result
            else:
                logger.warning("Categorization failed", error=str(category_result))
                analysis_results["categorization"] = {"error": str(category_result)}

            # Add sentiment analysis results
            if isinstance(sentiment_result, dict):
                analysis_results["sentiment"] = sentiment_result
            else:
                logger.warning("Sentiment analysis failed", error=str(sentiment_result))
                analysis_results["sentiment"] = {"error": str(sentiment_result)}

            # Add entity extraction results
            if isinstance(entities_result, dict):
                analysis_results["entities"] = entities_result
            else:
                logger.warning("Entity extraction failed", error=str(entities_result))
                analysis_results["entities"] = {"error": str(entities_result)}

            logger.info("Document analysis completed", document_id=document_id)
            return analysis_results

        except Exception as e:
            logger.error("Document analysis failed", document_id=document_id, error=str(e))
            raise ProcessingError(f"Document analysis failed: {str(e)}")

    async def generate_document_summary(
        self,
        document_id: int,
        text: str,
        style: str = "concise",
        use_llm: bool = True
    ) -> Dict:
        """
        Generate high-quality document summary.

        Args:
            document_id: Document ID
            text: Document text content
            style: Summary style ('concise', 'detailed', 'bullet-points')
            use_llm: Whether to use external LLM for summarization

        Returns:
            Dict: Summary with metadata
        """
        logger.info(
            "Generating document summary",
            document_id=document_id,
            style=style,
            use_llm=use_llm
        )

        try:
            if use_llm:
                # Try LLM-based summarization first
                try:
                    summary_result = await llm_service.summarize_with_llm(
                        text=text,
                        style=style,
                        max_length=500
                    )
                    summary_result["document_id"] = document_id
                    return summary_result

                except Exception as e:
                    logger.warning("LLM summarization failed, using extractive method", error=str(e))

            # Fallback to extractive summarization
            summary_result = await ai_features_service.summarize_document(
                text=text,
                max_length=500,
                style="extractive"
            )
            summary_result["document_id"] = document_id

            return summary_result

        except Exception as e:
            logger.error("Document summarization failed", document_id=document_id, error=str(e))
            raise ProcessingError(f"Document summarization failed: {str(e)}")

    async def delete_document_vectors(self, document_id: int) -> None:
        """
        Delete all vectors for a document.
        
        Args:
            document_id: Document ID
        """
        logger.info("Deleting document vectors", document_id=document_id)
        
        try:
            # Find all vector IDs for this document
            async with AsyncSessionLocal() as db:
                result = await db.execute(
                    select(DocumentChunk.vector_id)
                    .where(DocumentChunk.document_id == document_id)
                    .where(DocumentChunk.vector_id.isnot(None))
                )
                vector_ids = [row[0] for row in result.all()]
            
            if vector_ids:
                await self.vector_store.delete_vectors(vector_ids)
                logger.info(
                    "Document vectors deleted",
                    document_id=document_id,
                    vector_count=len(vector_ids),
                )
            else:
                logger.info("No vectors found for document", document_id=document_id)
                
        except Exception as e:
            logger.error(
                "Failed to delete document vectors",
                document_id=document_id,
                error=str(e),
            )
            raise VectorDatabaseError(f"Failed to delete document vectors: {str(e)}")
    
    async def get_service_status(self) -> Dict:
        """Get RAG service status."""
        try:
            # Check embedding service
            embedding_status = {
                "initialized": self.embedding_service.is_initialized,
                "model_name": self.embedding_service.model_name,
                "dimension": self.embedding_service.dimension,
            }
            
            # Check vector store
            vector_status = await self.vector_store.health_check()
            
            # Get collection info if available
            collection_info = {}
            try:
                collection_info = await self.vector_store.get_collection_info()
            except Exception:
                pass
            
            return {
                "status": "healthy" if embedding_status["initialized"] and vector_status["status"] == "healthy" else "unhealthy",
                "embedding_service": embedding_status,
                "vector_store": vector_status,
                "collection": collection_info,
            }
            
        except Exception as e:
            logger.error("Failed to get RAG service status", error=str(e))
            return {
                "status": "unhealthy",
                "error": str(e),
            }


# Global RAG service instance
rag_service = RAGService()
