'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Upload, 
  Search, 
  MessageSquare, 
  FolderOpen, 
  Eye,
  Plus,
  FileText,
  Bot,
  Sparkles
} from 'lucide-react'
import { DocumentUpload } from '@/components/documents/DocumentUpload'
import { DocumentDashboard } from '@/components/documents/DocumentDashboard'
import { DocumentSearch } from '@/components/documents/DocumentSearch'
import { DocumentChat } from '@/components/documents/DocumentChat'
import { DocumentViewer } from '@/components/documents/DocumentViewer'
import { useToast } from '@/hooks/use-toast'

interface ViewerDocument {
  id: number
  title: string
  type: 'pdf' | 'docx' | 'txt' | 'md'
  searchResults?: any[]
}

export default function DocumentsPage() {
  const [activeTab, setActiveTab] = useState('dashboard')
  const [viewerDocument, setViewerDocument] = useState<ViewerDocument | null>(null)
  const [showUpload, setShowUpload] = useState(false)
  const { toast } = useToast()

  const handleUploadComplete = (documents: any[]) => {
    toast({
      title: 'Upload Complete',
      description: `${documents.length} document(s) uploaded successfully`,
    })
    
    // Switch to dashboard to see uploaded documents
    setActiveTab('dashboard')
    setShowUpload(false)
  }

  const handleViewDocument = (document: ViewerDocument) => {
    setViewerDocument(document)
  }

  const handleSearchResultView = (documentId: number, title: string, type: string, searchResults: any[]) => {
    setViewerDocument({
      id: documentId,
      title,
      type: type as 'pdf' | 'docx' | 'txt' | 'md',
      searchResults,
    })
  }

  if (viewerDocument) {
    return (
      <div className="container mx-auto p-6 h-screen flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => setViewerDocument(null)}
            >
              ← Back to Documents
            </Button>
            <h1 className="text-2xl font-bold">Document Viewer</h1>
          </div>
        </div>
        
        <div className="flex-1">
          <DocumentViewer
            documentId={viewerDocument.id}
            documentTitle={viewerDocument.title}
            documentType={viewerDocument.type}
            searchResults={viewerDocument.searchResults}
            className="h-full"
          />
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Documents</h1>
          <p className="text-muted-foreground">
            Upload, manage, and search through your documents with AI
          </p>
        </div>
        
        <Button onClick={() => setShowUpload(true)} className="gap-2">
          <Plus className="h-4 w-4" />
          Upload Documents
        </Button>
      </div>

      {/* Upload Modal/Section */}
      {showUpload && (
        <Card className="border-2 border-primary/20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload New Documents
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowUpload(false)}
              >
                ✕
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <DocumentUpload onUploadComplete={handleUploadComplete} />
          </CardContent>
        </Card>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="search" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Search
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Chat
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Dashboard Tab */}
        <TabsContent value="dashboard" className="space-y-6">
          <DocumentDashboard />
        </TabsContent>

        {/* Search Tab */}
        <TabsContent value="search" className="space-y-6">
          <DocumentSearch />
        </TabsContent>

        {/* Chat Tab */}
        <TabsContent value="chat" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <DocumentChat />
            </div>
            
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Bot className="h-5 w-5" />
                    Chat Tips
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm space-y-2">
                    <p className="font-medium">Ask specific questions:</p>
                    <ul className="text-muted-foreground space-y-1 ml-4">
                      <li>• "What are the main findings in the research paper?"</li>
                      <li>• "Summarize the methodology section"</li>
                      <li>• "What are the key recommendations?"</li>
                    </ul>
                  </div>
                  
                  <div className="text-sm space-y-2">
                    <p className="font-medium">Reference specific documents:</p>
                    <ul className="text-muted-foreground space-y-1 ml-4">
                      <li>• "In the contract document, what are the payment terms?"</li>
                      <li>• "Compare the results from both studies"</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={() => setActiveTab('search')}
                  >
                    <Search className="h-4 w-4 mr-2" />
                    Search Documents
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={() => setShowUpload(true)}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Upload New Document
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={() => setActiveTab('dashboard')}
                  >
                    <FolderOpen className="h-4 w-4 mr-2" />
                    View All Documents
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Document Types
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">PDF Documents</span>
                    <Badge variant="secondary">45</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Word Documents</span>
                    <Badge variant="secondary">23</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Text Files</span>
                    <Badge variant="secondary">12</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Markdown Files</span>
                    <Badge variant="secondary">8</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="h-5 w-5" />
                  Search Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold">156</div>
                    <div className="text-sm text-muted-foreground">Total Searches</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">89%</div>
                    <div className="text-sm text-muted-foreground">Success Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">0.8s</div>
                    <div className="text-sm text-muted-foreground">Avg Response Time</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Chat Statistics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold">42</div>
                    <div className="text-sm text-muted-foreground">Conversations</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">234</div>
                    <div className="text-sm text-muted-foreground">Messages Sent</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">92%</div>
                    <div className="text-sm text-muted-foreground">Satisfaction Rate</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <Upload className="h-5 w-5 text-blue-500" />
                  <div className="flex-1">
                    <p className="font-medium">Uploaded "Research Paper.pdf"</p>
                    <p className="text-sm text-muted-foreground">2 hours ago</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <Search className="h-5 w-5 text-green-500" />
                  <div className="flex-1">
                    <p className="font-medium">Searched for "machine learning algorithms"</p>
                    <p className="text-sm text-muted-foreground">4 hours ago</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <MessageSquare className="h-5 w-5 text-purple-500" />
                  <div className="flex-1">
                    <p className="font-medium">Started chat conversation</p>
                    <p className="text-sm text-muted-foreground">1 day ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
