#!/bin/bash

# Lonors AI Agent Platform - Stop Development Services Script
# This script stops all development services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the project root
if [ ! -f "docker-compose.yml" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

echo
print_status "Stopping Lonors AI Agent Platform Development Environment"
echo "========================================================="

# Stop Docker services
print_status "Stopping Docker services..."
if docker-compose down; then
    print_success "Docker services stopped successfully"
else
    print_warning "Some Docker services may not have stopped cleanly"
fi

# Kill any remaining processes on our ports
print_status "Checking for remaining processes on development ports..."

PORTS=(3000 5500 5432 6333 6379 7474 7687 9090 3001)

for port in "${PORTS[@]}"; do
    if lsof -ti:$port > /dev/null 2>&1; then
        print_status "Killing process on port $port..."
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
    fi
done

# Clean up any orphaned containers
print_status "Cleaning up orphaned containers..."
docker-compose down --remove-orphans > /dev/null 2>&1 || true

# Optional: Clean up volumes (commented out for safety)
# print_status "Cleaning up volumes..."
# docker-compose down -v

echo
print_success "🛑 All Lonors development services have been stopped!"
echo

print_status "To start services again, run: ./scripts/dev.sh"
echo
