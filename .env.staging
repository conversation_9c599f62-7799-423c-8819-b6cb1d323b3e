# Lonors RAG Platform - Staging Environment Configuration

# Environment
ENVIRONMENT=staging
DEBUG=false
LOG_LEVEL=INFO

# Database Configuration
POSTGRES_DB=lonors_staging
POSTGRES_USER=lonors_staging
POSTGRES_PASSWORD=staging_secure_password_change_me
DATABASE_URL=***************************************************************************/lonors_staging

# Redis Configuration
REDIS_PASSWORD=staging_redis_password_change_me
REDIS_URL=redis://:staging_redis_password_change_me@redis:6379/0

# Qdrant Configuration
QDRANT_HOST=qdrant
QDRANT_PORT=6333
QDRANT_COLLECTION_NAME=documents_staging

# Security
SECRET_KEY=staging_secret_key_change_me_to_random_string
JWT_SECRET_KEY=staging_jwt_secret_change_me_to_random_string
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:5500,https://staging.lonors.com
ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
ALLOWED_HEADERS=*

# File Upload Configuration
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=104857600  # 100MB
ALLOWED_FILE_TYPES=pdf,docx,txt,md

# AI/ML Configuration
DEFAULT_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EMBEDDING_BATCH_SIZE=32
EMBEDDING_DIMENSION=384

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_APP_URL=http://localhost:5500
NEXT_PUBLIC_ENVIRONMENT=staging
NEXT_TELEMETRY_DISABLED=1

# Monitoring Configuration
SENTRY_DSN=https://<EMAIL>/project-id
GRAFANA_PASSWORD=staging_grafana_password_change_me

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=smtp_password_change_me
FROM_EMAIL=<EMAIL>

# External Services
OPENAI_API_KEY=sk-your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60  # seconds

# Session Configuration
SESSION_TIMEOUT=3600  # 1 hour

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=7

# SSL Configuration
SSL_ENABLED=false
SSL_CERT_PATH=/etc/ssl/certs/staging.crt
SSL_KEY_PATH=/etc/ssl/private/staging.key

# Performance Configuration
WORKER_PROCESSES=2
WORKER_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=65

# Cache Configuration
CACHE_TTL=3600  # 1 hour
CACHE_MAX_SIZE=1000

# Document Processing
MAX_CHUNK_SIZE=1000
CHUNK_OVERLAP=200
PROCESSING_TIMEOUT=300  # 5 minutes

# Search Configuration
DEFAULT_SEARCH_LIMIT=10
MAX_SEARCH_LIMIT=100
DEFAULT_SIMILARITY_THRESHOLD=0.7

# Chat Configuration
DEFAULT_CONTEXT_LIMIT=5
MAX_CONTEXT_LIMIT=10
CHAT_TIMEOUT=30  # seconds
