'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'
import { 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  Download, 
  ChevronLeft, 
  ChevronRight,
  Search,
  Highlight,
  FileText,
  Loader2,
  AlertCircle,
  Maximize,
  Minimize
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { useToast } from '@/hooks/use-toast'
import { cn } from '@/lib/utils'

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`

interface DocumentViewerProps {
  documentId: number
  documentTitle: string
  documentType: 'pdf' | 'docx' | 'txt' | 'md'
  searchResults?: Array<{
    chunk_index: number
    page_number?: number
    start_position?: number
    end_position?: number
    content: string
    similarity_score: number
  }>
  className?: string
}

interface TextContent {
  content: string
  highlights?: Array<{
    start: number
    end: number
    type: 'search' | 'result'
  }>
}

export function DocumentViewer({ 
  documentId, 
  documentTitle, 
  documentType, 
  searchResults = [],
  className 
}: DocumentViewerProps) {
  const [numPages, setNumPages] = useState<number>(0)
  const [pageNumber, setPageNumber] = useState<number>(1)
  const [scale, setScale] = useState<number>(1.0)
  const [rotation, setRotation] = useState<number>(0)
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false)
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [textContent, setTextContent] = useState<TextContent | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [documentUrl, setDocumentUrl] = useState<string | null>(null)
  const { toast } = useToast()

  // Load document URL
  useEffect(() => {
    const loadDocument = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch(`/api/v1/documents/${documentId}/download`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        })

        if (!response.ok) {
          throw new Error('Failed to load document')
        }

        const blob = await response.blob()
        const url = URL.createObjectURL(blob)
        setDocumentUrl(url)

        // For text-based documents, extract content
        if (documentType === 'txt' || documentType === 'md') {
          const text = await blob.text()
          setTextContent({ content: text })
        }

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load document')
      } finally {
        setIsLoading(false)
      }
    }

    loadDocument()

    // Cleanup URL on unmount
    return () => {
      if (documentUrl) {
        URL.revokeObjectURL(documentUrl)
      }
    }
  }, [documentId, documentType])

  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setNumPages(numPages)
    setIsLoading(false)
  }, [])

  const onDocumentLoadError = useCallback((error: Error) => {
    setError(error.message)
    setIsLoading(false)
  }, [])

  const goToPrevPage = () => {
    setPageNumber(prev => Math.max(1, prev - 1))
  }

  const goToNextPage = () => {
    setPageNumber(prev => Math.min(numPages, prev + 1))
  }

  const zoomIn = () => {
    setScale(prev => Math.min(3.0, prev + 0.2))
  }

  const zoomOut = () => {
    setScale(prev => Math.max(0.5, prev - 0.2))
  }

  const rotate = () => {
    setRotation(prev => (prev + 90) % 360)
  }

  const toggleFullscreen = () => {
    setIsFullscreen(prev => !prev)
  }

  const downloadDocument = async () => {
    if (!documentUrl) return

    try {
      const link = document.createElement('a')
      link.href = documentUrl
      link.download = documentTitle
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: 'Download Started',
        description: 'Document download has started',
      })
    } catch (error) {
      toast({
        title: 'Download Failed',
        description: 'Failed to download document',
        variant: 'destructive',
      })
    }
  }

  const jumpToSearchResult = (result: typeof searchResults[0]) => {
    if (result.page_number && documentType === 'pdf') {
      setPageNumber(result.page_number)
    }
    
    toast({
      title: 'Jumped to Result',
      description: `Navigated to page ${result.page_number || 'unknown'}`,
    })
  }

  const highlightText = (text: string, searchTerm: string) => {
    if (!searchTerm) return text

    const regex = new RegExp(`(${searchTerm})`, 'gi')
    return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800">$1</mark>')
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4" />
            <p className="text-lg font-medium">Loading document...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <p className="text-lg font-medium">Failed to load document</p>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Toolbar */}
      <Card className="flex-shrink-0">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg">
              <FileText className="h-5 w-5" />
              {documentTitle}
              <Badge variant="outline">{documentType.toUpperCase()}</Badge>
            </CardTitle>
            
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="sm" onClick={downloadDocument}>
                      <Download className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Download</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="sm" onClick={toggleFullscreen}>
                      {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>{isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="flex items-center justify-between gap-4">
            {/* Navigation Controls */}
            {documentType === 'pdf' && (
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={goToPrevPage} disabled={pageNumber <= 1}>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                
                <span className="text-sm font-medium min-w-20 text-center">
                  {pageNumber} / {numPages}
                </span>
                
                <Button variant="outline" size="sm" onClick={goToNextPage} disabled={pageNumber >= numPages}>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )}

            {/* Zoom Controls */}
            {documentType === 'pdf' && (
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={zoomOut} disabled={scale <= 0.5}>
                  <ZoomOut className="h-4 w-4" />
                </Button>
                
                <span className="text-sm font-medium min-w-16 text-center">
                  {Math.round(scale * 100)}%
                </span>
                
                <Button variant="outline" size="sm" onClick={zoomIn} disabled={scale >= 3.0}>
                  <ZoomIn className="h-4 w-4" />
                </Button>
                
                <Button variant="outline" size="sm" onClick={rotate}>
                  <RotateCw className="h-4 w-4" />
                </Button>
              </div>
            )}

            {/* Search */}
            <div className="flex items-center gap-2 flex-1 max-w-md">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search in document..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content Area */}
      <div className="flex-1 flex gap-4 p-4">
        {/* Main Document View */}
        <div className="flex-1">
          <Card className="h-full">
            <CardContent className="p-0 h-full">
              <ScrollArea className="h-full">
                <div className="p-4">
                  {documentType === 'pdf' && documentUrl ? (
                    <div className="flex justify-center">
                      <Document
                        file={documentUrl}
                        onLoadSuccess={onDocumentLoadSuccess}
                        onLoadError={onDocumentLoadError}
                        loading={
                          <div className="flex items-center justify-center h-96">
                            <Loader2 className="h-8 w-8 animate-spin" />
                          </div>
                        }
                      >
                        <Page
                          pageNumber={pageNumber}
                          scale={scale}
                          rotate={rotation}
                          loading={
                            <div className="flex items-center justify-center h-96">
                              <Loader2 className="h-8 w-8 animate-spin" />
                            </div>
                          }
                        />
                      </Document>
                    </div>
                  ) : textContent ? (
                    <div className="prose prose-sm max-w-none">
                      <pre 
                        className="whitespace-pre-wrap font-mono text-sm leading-relaxed"
                        dangerouslySetInnerHTML={{ 
                          __html: highlightText(textContent.content, searchTerm) 
                        }}
                      />
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-96">
                      <div className="text-center">
                        <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-lg font-medium">Document type not supported</p>
                        <p className="text-muted-foreground">Cannot preview {documentType.toUpperCase()} files</p>
                      </div>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        {/* Search Results Sidebar */}
        {searchResults.length > 0 && (
          <div className="w-80">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Highlight className="h-4 w-4" />
                  Search Results ({searchResults.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <ScrollArea className="h-full">
                  <div className="p-4 space-y-3">
                    {searchResults.map((result, index) => (
                      <div
                        key={index}
                        className="p-3 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                        onClick={() => jumpToSearchResult(result)}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <Badge variant="secondary" className="text-xs">
                            Chunk {result.chunk_index + 1}
                          </Badge>
                          {result.page_number && (
                            <Badge variant="outline" className="text-xs">
                              Page {result.page_number}
                            </Badge>
                          )}
                        </div>
                        
                        <p className="text-sm leading-relaxed mb-2">
                          {result.content.substring(0, 150)}...
                        </p>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-muted-foreground">
                            {Math.round(result.similarity_score * 100)}% match
                          </span>
                          <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                            Jump to
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
