"""Tests for RAG service."""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from app.core.exceptions import ProcessingError
from app.services.rag_service import RAGService


class TestRAGService:
    """Test RAG service functionality."""
    
    @pytest.fixture
    def rag_service(self):
        """Create RAG service instance."""
        return RAGService()
    
    @pytest.fixture
    def mock_embedding_service(self):
        """Mock embedding service."""
        mock_service = MagicMock()
        mock_service.is_initialized = True
        mock_service.dimension = 384
        mock_service.model_name = "test-model"
        mock_service.initialize = AsyncMock()
        mock_service.generate_embedding = AsyncMock(return_value=[0.1, 0.2, 0.3])
        mock_service.generate_embeddings_batch = AsyncMock(
            return_value=[[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]]
        )
        return mock_service
    
    @pytest.fixture
    def mock_vector_store(self):
        """Mock vector store."""
        mock_store = MagicMock()
        mock_store.initialize = AsyncMock()
        mock_store.create_collection = AsyncMock()
        mock_store.add_vectors = AsyncMock(return_value=["vec1", "vec2"])
        mock_store.search_vectors = AsyncMock(return_value=[
            ("vec1", 0.9, {"document_id": 1, "content": "test content"}),
            ("vec2", 0.8, {"document_id": 2, "content": "more content"}),
        ])
        mock_store.delete_vectors = AsyncMock()
        mock_store.health_check = AsyncMock(return_value={"status": "healthy"})
        mock_store.get_collection_info = AsyncMock(return_value={"vectors_count": 100})
        return mock_store
    
    @pytest.mark.asyncio
    async def test_initialize_success(self, rag_service, mock_embedding_service, mock_vector_store):
        """Test successful RAG service initialization."""
        with patch.object(rag_service, 'embedding_service', mock_embedding_service), \
             patch.object(rag_service, 'vector_store', mock_vector_store):
            
            await rag_service.initialize()
            
            mock_embedding_service.initialize.assert_called_once()
            mock_vector_store.initialize.assert_called_once()
            mock_vector_store.create_collection.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_document_chunks_success(
        self, 
        rag_service, 
        mock_embedding_service, 
        mock_vector_store
    ):
        """Test successful document chunk processing."""
        chunks = [
            {
                "id": 1,
                "content": "Test content 1",
                "chunk_index": 0,
                "character_count": 14,
                "token_count": 3,
                "document_title": "Test Doc",
                "document_type": "txt",
            },
            {
                "id": 2,
                "content": "Test content 2",
                "chunk_index": 1,
                "character_count": 14,
                "token_count": 3,
                "document_title": "Test Doc",
                "document_type": "txt",
            },
        ]
        
        with patch.object(rag_service, 'embedding_service', mock_embedding_service), \
             patch.object(rag_service, 'vector_store', mock_vector_store), \
             patch.object(rag_service, '_update_chunk_vector_ids', AsyncMock()):
            
            await rag_service.process_document_chunks(1, chunks)
            
            mock_embedding_service.generate_embeddings_batch.assert_called_once()
            mock_vector_store.add_vectors.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_document_chunks_empty_list(self, rag_service):
        """Test processing empty chunk list."""
        await rag_service.process_document_chunks(1, [])
        # Should complete without error
    
    @pytest.mark.asyncio
    async def test_semantic_search_success(
        self, 
        rag_service, 
        mock_embedding_service, 
        mock_vector_store
    ):
        """Test successful semantic search."""
        with patch.object(rag_service, 'embedding_service', mock_embedding_service), \
             patch.object(rag_service, 'vector_store', mock_vector_store), \
             patch.object(rag_service, '_enrich_search_results', AsyncMock(return_value=[
                 {
                     "vector_id": "vec1",
                     "similarity_score": 0.9,
                     "chunk_id": 1,
                     "content": "test content",
                     "document_id": 1,
                     "document_title": "Test Doc",
                 }
             ])):
            
            results = await rag_service.semantic_search(
                query="test query",
                limit=10,
                threshold=0.7,
                user_id=1,
            )
            
            assert len(results) == 1
            assert results[0]["similarity_score"] == 0.9
            mock_embedding_service.generate_embedding.assert_called_once_with("test query")
            mock_vector_store.search_vectors.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_semantic_search_empty_query(self, rag_service):
        """Test semantic search with empty query."""
        with pytest.raises(ProcessingError, match="Query cannot be empty"):
            await rag_service.semantic_search("")
    
    @pytest.mark.asyncio
    async def test_generate_rag_response_success(self, rag_service):
        """Test successful RAG response generation."""
        mock_search_results = [
            {
                "content": "This is relevant content about the query.",
                "document_title": "Test Document",
                "page_number": 1,
                "similarity_score": 0.9,
            }
        ]
        
        with patch.object(rag_service, 'semantic_search', AsyncMock(return_value=mock_search_results)):
            response = await rag_service.generate_rag_response(
                query="test query",
                context_limit=5,
                user_id=1,
            )
            
            assert response["query"] == "test query"
            assert "response" in response
            assert len(response["context"]) == 1
            assert len(response["sources"]) == 1
            assert response["confidence"] > 0
    
    @pytest.mark.asyncio
    async def test_generate_rag_response_no_results(self, rag_service):
        """Test RAG response generation with no search results."""
        with patch.object(rag_service, 'semantic_search', AsyncMock(return_value=[])):
            response = await rag_service.generate_rag_response(
                query="test query",
                user_id=1,
            )
            
            assert "couldn't find relevant information" in response["response"]
            assert response["confidence"] == 0.0
            assert len(response["context"]) == 0
    
    @pytest.mark.asyncio
    async def test_delete_document_vectors_success(self, rag_service, mock_vector_store):
        """Test successful document vector deletion."""
        with patch.object(rag_service, 'vector_store', mock_vector_store), \
             patch('app.services.rag_service.AsyncSessionLocal') as mock_session:
            
            # Mock database session
            mock_db = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_db
            
            # Mock query result
            mock_result = MagicMock()
            mock_result.all.return_value = [("vec1",), ("vec2",)]
            mock_db.execute.return_value = mock_result
            
            await rag_service.delete_document_vectors(1)
            
            mock_vector_store.delete_vectors.assert_called_once_with(["vec1", "vec2"])
    
    @pytest.mark.asyncio
    async def test_delete_document_vectors_no_vectors(self, rag_service, mock_vector_store):
        """Test document vector deletion with no vectors."""
        with patch.object(rag_service, 'vector_store', mock_vector_store), \
             patch('app.services.rag_service.AsyncSessionLocal') as mock_session:
            
            # Mock database session
            mock_db = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_db
            
            # Mock empty query result
            mock_result = MagicMock()
            mock_result.all.return_value = []
            mock_db.execute.return_value = mock_result
            
            await rag_service.delete_document_vectors(1)
            
            # Should not call delete_vectors
            mock_vector_store.delete_vectors.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_get_service_status_healthy(
        self, 
        rag_service, 
        mock_embedding_service, 
        mock_vector_store
    ):
        """Test getting healthy service status."""
        with patch.object(rag_service, 'embedding_service', mock_embedding_service), \
             patch.object(rag_service, 'vector_store', mock_vector_store):
            
            status = await rag_service.get_service_status()
            
            assert status["status"] == "healthy"
            assert status["embedding_service"]["initialized"] is True
            assert status["vector_store"]["status"] == "healthy"
            assert "collection" in status
    
    @pytest.mark.asyncio
    async def test_get_service_status_unhealthy(self, rag_service):
        """Test getting unhealthy service status."""
        mock_embedding_service = MagicMock()
        mock_embedding_service.is_initialized = False
        
        mock_vector_store = MagicMock()
        mock_vector_store.health_check = AsyncMock(return_value={"status": "unhealthy"})
        
        with patch.object(rag_service, 'embedding_service', mock_embedding_service), \
             patch.object(rag_service, 'vector_store', mock_vector_store):
            
            status = await rag_service.get_service_status()
            
            assert status["status"] == "unhealthy"
    
    def test_generate_simple_response(self, rag_service):
        """Test simple response generation."""
        context_chunks = [
            {
                "content": "This is test content.",
                "source": "Test Document (p. 1)",
            }
        ]
        
        response = rag_service._generate_simple_response("test query", context_chunks)
        
        assert "Based on the available documents" in response
        assert "This is test content" in response
    
    def test_generate_simple_response_empty_context(self, rag_service):
        """Test simple response generation with empty context."""
        response = rag_service._generate_simple_response("test query", [])
        
        assert "couldn't find relevant information" in response
