"""Tests for document API endpoints."""

import io
import tempfile
from pathlib import Path
from unittest.mock import patch

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.document import Document, DocumentStatusEnum, DocumentTypeEnum
from app.models.user import User


class TestDocumentUpload:
    """Test document upload functionality."""
    
    @pytest.mark.asyncio
    async def test_upload_text_document_success(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test successful text document upload."""
        # Create test file
        file_content = b"This is a test document for upload testing."
        files = {
            "file": ("test.txt", io.BytesIO(file_content), "text/plain")
        }
        data = {
            "title": "Test Document",
            "tags": "test,upload"
        }
        
        response = await async_client.post(
            "/api/v1/documents/upload",
            files=files,
            data=data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        result = response.json()
        
        assert result["title"] == "Test Document"
        assert result["filename"] == "test.txt"
        assert result["document_type"] == "txt"
        assert result["status"] == "uploaded"
        assert result["file_size"] == len(file_content)
        assert "id" in result
        assert "created_at" in result
    
    @pytest.mark.asyncio
    async def test_upload_document_without_title(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test document upload without title uses filename."""
        file_content = b"Test content"
        files = {
            "file": ("my-document.txt", io.BytesIO(file_content), "text/plain")
        }
        
        response = await async_client.post(
            "/api/v1/documents/upload",
            files=files,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        result = response.json()
        assert result["title"] == "my-document.txt"
    
    @pytest.mark.asyncio
    async def test_upload_document_unauthorized(self, async_client: AsyncClient):
        """Test document upload without authentication."""
        file_content = b"Test content"
        files = {
            "file": ("test.txt", io.BytesIO(file_content), "text/plain")
        }
        
        response = await async_client.post(
            "/api/v1/documents/upload",
            files=files
        )
        
        assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_upload_empty_file_fails(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test upload fails for empty file."""
        files = {
            "file": ("empty.txt", io.BytesIO(b""), "text/plain")
        }
        
        response = await async_client.post(
            "/api/v1/documents/upload",
            files=files,
            headers=auth_headers
        )
        
        assert response.status_code == 422
        assert "empty" in response.json()["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_upload_unsupported_file_type_fails(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test upload fails for unsupported file type."""
        file_content = b"fake executable content"
        files = {
            "file": ("malware.exe", io.BytesIO(file_content), "application/x-executable")
        }
        
        response = await async_client.post(
            "/api/v1/documents/upload",
            files=files,
            headers=auth_headers
        )
        
        assert response.status_code == 422
        assert "unsupported" in response.json()["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_upload_oversized_file_fails(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test upload fails for oversized file."""
        # Create large file content (101MB)
        large_content = b"x" * (101 * 1024 * 1024)
        files = {
            "file": ("large.txt", io.BytesIO(large_content), "text/plain")
        }
        
        response = await async_client.post(
            "/api/v1/documents/upload",
            files=files,
            headers=auth_headers
        )
        
        assert response.status_code == 422
        assert "too large" in response.json()["detail"].lower()


class TestDocumentListing:
    """Test document listing functionality."""
    
    @pytest.mark.asyncio
    async def test_list_documents_empty(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test listing documents when user has none."""
        response = await async_client.get(
            "/api/v1/documents/",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        assert response.json() == []
    
    @pytest.mark.asyncio
    async def test_list_documents_with_data(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict,
        db_session: AsyncSession,
        test_user: User
    ):
        """Test listing documents with existing data."""
        # Create test documents
        doc1 = Document(
            title="Document 1",
            filename="doc1.txt",
            file_path="/fake/path1.txt",
            file_size=1000,
            document_type=DocumentTypeEnum.TXT,
            mime_type="text/plain",
            content_hash="hash1",
            status=DocumentStatusEnum.PROCESSED,
            owner_id=test_user.id,
            chunk_count=5
        )
        
        doc2 = Document(
            title="Document 2",
            filename="doc2.pdf",
            file_path="/fake/path2.pdf",
            file_size=2000,
            document_type=DocumentTypeEnum.PDF,
            mime_type="application/pdf",
            content_hash="hash2",
            status=DocumentStatusEnum.PROCESSING,
            owner_id=test_user.id,
            chunk_count=0
        )
        
        db_session.add_all([doc1, doc2])
        await db_session.commit()
        
        response = await async_client.get(
            "/api/v1/documents/",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        documents = response.json()
        assert len(documents) == 2
        
        # Check document data
        doc_titles = [doc["title"] for doc in documents]
        assert "Document 1" in doc_titles
        assert "Document 2" in doc_titles
    
    @pytest.mark.asyncio
    async def test_list_documents_with_search(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict,
        db_session: AsyncSession,
        test_user: User
    ):
        """Test listing documents with search filter."""
        # Create test documents
        doc1 = Document(
            title="Machine Learning Guide",
            filename="ml.pdf",
            file_path="/fake/ml.pdf",
            file_size=1000,
            document_type=DocumentTypeEnum.PDF,
            mime_type="application/pdf",
            content_hash="hash1",
            status=DocumentStatusEnum.PROCESSED,
            owner_id=test_user.id
        )
        
        doc2 = Document(
            title="Python Programming",
            filename="python.txt",
            file_path="/fake/python.txt",
            file_size=2000,
            document_type=DocumentTypeEnum.TXT,
            mime_type="text/plain",
            content_hash="hash2",
            status=DocumentStatusEnum.PROCESSED,
            owner_id=test_user.id
        )
        
        db_session.add_all([doc1, doc2])
        await db_session.commit()
        
        # Search for "machine"
        response = await async_client.get(
            "/api/v1/documents/?search=machine",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        documents = response.json()
        assert len(documents) == 1
        assert documents[0]["title"] == "Machine Learning Guide"
    
    @pytest.mark.asyncio
    async def test_list_documents_with_status_filter(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict,
        db_session: AsyncSession,
        test_user: User
    ):
        """Test listing documents with status filter."""
        # Create test documents with different statuses
        doc1 = Document(
            title="Processed Doc",
            filename="processed.txt",
            file_path="/fake/processed.txt",
            file_size=1000,
            document_type=DocumentTypeEnum.TXT,
            mime_type="text/plain",
            content_hash="hash1",
            status=DocumentStatusEnum.PROCESSED,
            owner_id=test_user.id
        )
        
        doc2 = Document(
            title="Processing Doc",
            filename="processing.txt",
            file_path="/fake/processing.txt",
            file_size=2000,
            document_type=DocumentTypeEnum.TXT,
            mime_type="text/plain",
            content_hash="hash2",
            status=DocumentStatusEnum.PROCESSING,
            owner_id=test_user.id
        )
        
        db_session.add_all([doc1, doc2])
        await db_session.commit()
        
        # Filter by processed status
        response = await async_client.get(
            "/api/v1/documents/?status=processed",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        documents = response.json()
        assert len(documents) == 1
        assert documents[0]["title"] == "Processed Doc"
        assert documents[0]["status"] == "processed"


class TestDocumentRetrieval:
    """Test document retrieval functionality."""
    
    @pytest.mark.asyncio
    async def test_get_document_success(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict,
        db_session: AsyncSession,
        test_user: User
    ):
        """Test successful document retrieval."""
        # Create test document
        document = Document(
            title="Test Document",
            filename="test.txt",
            file_path="/fake/test.txt",
            file_size=1000,
            document_type=DocumentTypeEnum.TXT,
            mime_type="text/plain",
            content_hash="testhash",
            status=DocumentStatusEnum.PROCESSED,
            owner_id=test_user.id,
            chunk_count=5
        )
        
        db_session.add(document)
        await db_session.commit()
        await db_session.refresh(document)
        
        response = await async_client.get(
            f"/api/v1/documents/{document.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert result["id"] == document.id
        assert result["title"] == "Test Document"
        assert result["filename"] == "test.txt"
        assert result["status"] == "processed"
        assert result["chunk_count"] == 5
    
    @pytest.mark.asyncio
    async def test_get_document_not_found(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test document retrieval for non-existent document."""
        response = await async_client.get(
            "/api/v1/documents/999999",
            headers=auth_headers
        )
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_get_document_unauthorized(self, async_client: AsyncClient):
        """Test document retrieval without authentication."""
        response = await async_client.get("/api/v1/documents/1")
        
        assert response.status_code == 401


class TestDocumentDeletion:
    """Test document deletion functionality."""
    
    @pytest.mark.asyncio
    async def test_delete_document_success(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict,
        db_session: AsyncSession,
        test_user: User
    ):
        """Test successful document deletion."""
        # Create test document
        document = Document(
            title="To Delete",
            filename="delete.txt",
            file_path="/fake/delete.txt",
            file_size=1000,
            document_type=DocumentTypeEnum.TXT,
            mime_type="text/plain",
            content_hash="deletehash",
            status=DocumentStatusEnum.PROCESSED,
            owner_id=test_user.id
        )
        
        db_session.add(document)
        await db_session.commit()
        await db_session.refresh(document)
        
        response = await async_client.delete(
            f"/api/v1/documents/{document.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 204
        
        # Verify document is deleted
        get_response = await async_client.get(
            f"/api/v1/documents/{document.id}",
            headers=auth_headers
        )
        assert get_response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_delete_document_not_found(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test deletion of non-existent document."""
        response = await async_client.delete(
            "/api/v1/documents/999999",
            headers=auth_headers
        )
        
        assert response.status_code == 404
