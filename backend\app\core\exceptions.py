"""Custom exception classes for the application."""

from typing import Any, Dict, Optional


class AppException(Exception):
    """Base application exception."""
    
    def __init__(
        self,
        detail: str,
        status_code: int = 500,
        error_code: str = "INTERNAL_ERROR",
        headers: Optional[Dict[str, Any]] = None,
    ):
        self.detail = detail
        self.status_code = status_code
        self.error_code = error_code
        self.headers = headers or {}
        super().__init__(detail)


class ValidationError(AppException):
    """Validation error exception."""
    
    def __init__(self, detail: str, field: Optional[str] = None):
        super().__init__(
            detail=detail,
            status_code=422,
            error_code="VALIDATION_ERROR",
        )
        self.field = field


class AuthenticationError(AppException):
    """Authentication error exception."""
    
    def __init__(self, detail: str = "Authentication failed"):
        super().__init__(
            detail=detail,
            status_code=401,
            error_code="AUTHENTICATION_ERROR",
            headers={"WWW-Authenticate": "Bearer"},
        )


class AuthorizationError(AppException):
    """Authorization error exception."""
    
    def __init__(self, detail: str = "Insufficient permissions"):
        super().__init__(
            detail=detail,
            status_code=403,
            error_code="AUTHORIZATION_ERROR",
        )


class NotFoundError(AppException):
    """Resource not found exception."""
    
    def __init__(self, detail: str = "Resource not found", resource: Optional[str] = None):
        super().__init__(
            detail=detail,
            status_code=404,
            error_code="NOT_FOUND_ERROR",
        )
        self.resource = resource


class ConflictError(AppException):
    """Resource conflict exception."""
    
    def __init__(self, detail: str = "Resource conflict", resource: Optional[str] = None):
        super().__init__(
            detail=detail,
            status_code=409,
            error_code="CONFLICT_ERROR",
        )
        self.resource = resource


class RateLimitError(AppException):
    """Rate limit exceeded exception."""
    
    def __init__(self, detail: str = "Rate limit exceeded", retry_after: Optional[int] = None):
        headers = {}
        if retry_after:
            headers["Retry-After"] = str(retry_after)
        
        super().__init__(
            detail=detail,
            status_code=429,
            error_code="RATE_LIMIT_ERROR",
            headers=headers,
        )


class ExternalServiceError(AppException):
    """External service error exception."""
    
    def __init__(self, detail: str, service: str, status_code: int = 502):
        super().__init__(
            detail=detail,
            status_code=status_code,
            error_code="EXTERNAL_SERVICE_ERROR",
        )
        self.service = service


class DatabaseError(AppException):
    """Database error exception."""
    
    def __init__(self, detail: str = "Database operation failed"):
        super().__init__(
            detail=detail,
            status_code=500,
            error_code="DATABASE_ERROR",
        )


class CacheError(AppException):
    """Cache error exception."""
    
    def __init__(self, detail: str = "Cache operation failed"):
        super().__init__(
            detail=detail,
            status_code=500,
            error_code="CACHE_ERROR",
        )


class VectorDatabaseError(AppException):
    """Vector database error exception."""
    
    def __init__(self, detail: str = "Vector database operation failed"):
        super().__init__(
            detail=detail,
            status_code=500,
            error_code="VECTOR_DATABASE_ERROR",
        )


class ModelError(AppException):
    """AI model error exception."""
    
    def __init__(self, detail: str, model_name: Optional[str] = None):
        super().__init__(
            detail=detail,
            status_code=500,
            error_code="MODEL_ERROR",
        )
        self.model_name = model_name


class ProcessingError(AppException):
    """Data processing error exception."""
    
    def __init__(self, detail: str, operation: Optional[str] = None):
        super().__init__(
            detail=detail,
            status_code=500,
            error_code="PROCESSING_ERROR",
        )
        self.operation = operation


class ConfigurationError(AppException):
    """Configuration error exception."""
    
    def __init__(self, detail: str, config_key: Optional[str] = None):
        super().__init__(
            detail=detail,
            status_code=500,
            error_code="CONFIGURATION_ERROR",
        )
        self.config_key = config_key


class SecurityError(AppException):
    """Security error exception."""
    
    def __init__(self, detail: str = "Security violation detected"):
        super().__init__(
            detail=detail,
            status_code=403,
            error_code="SECURITY_ERROR",
        )


class QuotaExceededError(AppException):
    """Quota exceeded error exception."""
    
    def __init__(self, detail: str = "Quota exceeded", quota_type: Optional[str] = None):
        super().__init__(
            detail=detail,
            status_code=429,
            error_code="QUOTA_EXCEEDED_ERROR",
        )
        self.quota_type = quota_type


class MaintenanceError(AppException):
    """Maintenance mode error exception."""
    
    def __init__(self, detail: str = "Service temporarily unavailable"):
        super().__init__(
            detail=detail,
            status_code=503,
            error_code="MAINTENANCE_ERROR",
        )


# Exception mapping for common HTTP status codes
HTTP_EXCEPTION_MAP = {
    400: ValidationError,
    401: AuthenticationError,
    403: AuthorizationError,
    404: NotFoundError,
    409: ConflictError,
    429: RateLimitError,
    500: AppException,
    502: ExternalServiceError,
    503: MaintenanceError,
}
