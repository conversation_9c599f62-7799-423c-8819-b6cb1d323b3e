#!/usr/bin/env python3
"""
Comprehensive health check script for Lonors RAG Platform
"""

import asyncio
import json
import sys
import time
from datetime import datetime
from typing import Dict, List, Optional

import aiohttp
import asyncpg
import redis.asyncio as redis
from qdrant_client import QdrantClient
from qdrant_client.http.exceptions import ResponseHandlingException


class HealthChecker:
    """Comprehensive health checker for all system components."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.results = {}
        
    async def check_all(self) -> Dict:
        """Run all health checks."""
        print("🏥 Starting comprehensive health check...")
        
        checks = [
            ("Backend API", self.check_backend_api),
            ("Frontend", self.check_frontend),
            ("PostgreSQL", self.check_postgresql),
            ("Redis", self.check_redis),
            ("Qdrant", self.check_qdrant),
            ("File System", self.check_file_system),
            ("External Services", self.check_external_services),
        ]
        
        for name, check_func in checks:
            print(f"🔍 Checking {name}...")
            try:
                result = await check_func()
                self.results[name] = result
                status = "✅ HEALTHY" if result["healthy"] else "❌ UNHEALTHY"
                print(f"   {status} - {result.get('message', 'OK')}")
            except Exception as e:
                self.results[name] = {
                    "healthy": False,
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat()
                }
                print(f"   ❌ ERROR - {str(e)}")
        
        return self.results
    
    async def check_backend_api(self) -> Dict:
        """Check backend API health."""
        url = f"{self.config['backend_url']}/health"
        
        async with aiohttp.ClientSession() as session:
            try:
                start_time = time.time()
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "healthy": True,
                            "status_code": response.status,
                            "response_time": response_time,
                            "details": data,
                            "timestamp": datetime.utcnow().isoformat()
                        }
                    else:
                        return {
                            "healthy": False,
                            "status_code": response.status,
                            "response_time": response_time,
                            "message": f"HTTP {response.status}",
                            "timestamp": datetime.utcnow().isoformat()
                        }
            except asyncio.TimeoutError:
                return {
                    "healthy": False,
                    "error": "Request timeout",
                    "timestamp": datetime.utcnow().isoformat()
                }
            except Exception as e:
                return {
                    "healthy": False,
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat()
                }
    
    async def check_frontend(self) -> Dict:
        """Check frontend availability."""
        url = self.config['frontend_url']
        
        async with aiohttp.ClientSession() as session:
            try:
                start_time = time.time()
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    response_time = time.time() - start_time
                    
                    return {
                        "healthy": response.status == 200,
                        "status_code": response.status,
                        "response_time": response_time,
                        "timestamp": datetime.utcnow().isoformat()
                    }
            except Exception as e:
                return {
                    "healthy": False,
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat()
                }
    
    async def check_postgresql(self) -> Dict:
        """Check PostgreSQL database health."""
        try:
            start_time = time.time()
            conn = await asyncpg.connect(self.config['database_url'])
            
            # Test basic query
            result = await conn.fetchval("SELECT 1")
            
            # Check database stats
            stats = await conn.fetchrow("""
                SELECT 
                    count(*) as total_connections,
                    sum(case when state = 'active' then 1 else 0 end) as active_connections
                FROM pg_stat_activity
            """)
            
            await conn.close()
            response_time = time.time() - start_time
            
            return {
                "healthy": result == 1,
                "response_time": response_time,
                "connections": {
                    "total": stats['total_connections'],
                    "active": stats['active_connections']
                },
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def check_redis(self) -> Dict:
        """Check Redis cache health."""
        try:
            start_time = time.time()
            r = redis.from_url(self.config['redis_url'])
            
            # Test basic operations
            await r.set("health_check", "ok", ex=60)
            result = await r.get("health_check")
            await r.delete("health_check")
            
            # Get Redis info
            info = await r.info()
            
            await r.close()
            response_time = time.time() - start_time
            
            return {
                "healthy": result == b"ok",
                "response_time": response_time,
                "memory_usage": info.get('used_memory_human'),
                "connected_clients": info.get('connected_clients'),
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def check_qdrant(self) -> Dict:
        """Check Qdrant vector database health."""
        try:
            start_time = time.time()
            client = QdrantClient(
                host=self.config['qdrant_host'],
                port=self.config['qdrant_port']
            )
            
            # Test connection
            collections = client.get_collections()
            
            # Check specific collection if it exists
            collection_info = None
            try:
                collection_info = client.get_collection(self.config['qdrant_collection'])
            except ResponseHandlingException:
                pass  # Collection might not exist yet
            
            response_time = time.time() - start_time
            
            return {
                "healthy": True,
                "response_time": response_time,
                "collections_count": len(collections.collections),
                "collection_info": {
                    "vectors_count": collection_info.vectors_count if collection_info else 0,
                    "status": collection_info.status if collection_info else "not_found"
                },
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def check_file_system(self) -> Dict:
        """Check file system health."""
        import shutil
        import os
        
        try:
            upload_dir = self.config.get('upload_dir', '/app/uploads')
            
            # Check if upload directory exists and is writable
            if not os.path.exists(upload_dir):
                return {
                    "healthy": False,
                    "error": f"Upload directory {upload_dir} does not exist",
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            if not os.access(upload_dir, os.W_OK):
                return {
                    "healthy": False,
                    "error": f"Upload directory {upload_dir} is not writable",
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            # Check disk space
            total, used, free = shutil.disk_usage(upload_dir)
            free_percentage = (free / total) * 100
            
            return {
                "healthy": free_percentage > 10,  # Alert if less than 10% free
                "disk_usage": {
                    "total_gb": round(total / (1024**3), 2),
                    "used_gb": round(used / (1024**3), 2),
                    "free_gb": round(free / (1024**3), 2),
                    "free_percentage": round(free_percentage, 2)
                },
                "upload_dir": upload_dir,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def check_external_services(self) -> Dict:
        """Check external service dependencies."""
        results = {}
        
        # Check if we can reach external APIs (if configured)
        external_services = [
            ("OpenAI", "https://api.openai.com/v1/models"),
            ("Anthropic", "https://api.anthropic.com/v1/messages"),
        ]
        
        async with aiohttp.ClientSession() as session:
            for service_name, url in external_services:
                try:
                    start_time = time.time()
                    async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                        response_time = time.time() - start_time
                        results[service_name] = {
                            "healthy": response.status in [200, 401],  # 401 is OK (auth required)
                            "status_code": response.status,
                            "response_time": response_time
                        }
                except Exception as e:
                    results[service_name] = {
                        "healthy": False,
                        "error": str(e)
                    }
        
        overall_healthy = all(result["healthy"] for result in results.values())
        
        return {
            "healthy": overall_healthy,
            "services": results,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    def generate_report(self) -> str:
        """Generate a comprehensive health report."""
        overall_healthy = all(result["healthy"] for result in self.results.values())
        
        report = f"""
🏥 LONORS RAG PLATFORM HEALTH REPORT
{'='*50}

Overall Status: {'✅ HEALTHY' if overall_healthy else '❌ UNHEALTHY'}
Timestamp: {datetime.utcnow().isoformat()}

Component Status:
"""
        
        for component, result in self.results.items():
            status = "✅ HEALTHY" if result["healthy"] else "❌ UNHEALTHY"
            report += f"  {component}: {status}\n"
            
            if not result["healthy"] and "error" in result:
                report += f"    Error: {result['error']}\n"
            
            if "response_time" in result:
                report += f"    Response Time: {result['response_time']:.3f}s\n"
        
        return report


async def main():
    """Main health check function."""
    config = {
        "backend_url": "http://localhost:3000",
        "frontend_url": "http://localhost:5500",
        "database_url": "postgresql://lonors:password@localhost:5432/lonors",
        "redis_url": "redis://localhost:6379/0",
        "qdrant_host": "localhost",
        "qdrant_port": 6333,
        "qdrant_collection": "documents",
        "upload_dir": "/app/uploads"
    }
    
    # Override with environment variables if available
    import os
    config.update({
        "backend_url": os.getenv("BACKEND_URL", config["backend_url"]),
        "frontend_url": os.getenv("FRONTEND_URL", config["frontend_url"]),
        "database_url": os.getenv("DATABASE_URL", config["database_url"]),
        "redis_url": os.getenv("REDIS_URL", config["redis_url"]),
        "qdrant_host": os.getenv("QDRANT_HOST", config["qdrant_host"]),
        "qdrant_port": int(os.getenv("QDRANT_PORT", config["qdrant_port"])),
        "upload_dir": os.getenv("UPLOAD_DIR", config["upload_dir"]),
    })
    
    checker = HealthChecker(config)
    results = await checker.check_all()
    
    # Generate and print report
    report = checker.generate_report()
    print(report)
    
    # Save results to file
    with open("/tmp/health-check-results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    # Exit with appropriate code
    overall_healthy = all(result["healthy"] for result in results.values())
    sys.exit(0 if overall_healthy else 1)


if __name__ == "__main__":
    asyncio.run(main())
