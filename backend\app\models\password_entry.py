"""Password manager database models."""

from datetime import datetime
from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, ForeignKey, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base


class PasswordCategory(Base):
    """Password category model for organizing password entries."""
    
    __tablename__ = "password_categories"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Category details
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    color: Mapped[Optional[str]] = mapped_column(String(7))  # Hex color code
    icon: Mapped[Optional[str]] = mapped_column(String(50))  # Icon name
    
    # Ownership
    owner_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships
    owner: Mapped["User"] = relationship("User")
    password_entries: Mapped[list["PasswordEntry"]] = relationship(
        "PasswordEntry", 
        back_populates="category",
        cascade="all, delete-orphan"
    )


class PasswordEntry(Base):
    """Password entry model for secure credential storage."""
    
    __tablename__ = "password_entries"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Entry details
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    website_url: Mapped[Optional[str]] = mapped_column(String(500))
    username: Mapped[Optional[str]] = mapped_column(String(255))
    email: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Encrypted fields (these would be encrypted before storage)
    encrypted_password: Mapped[str] = mapped_column(Text, nullable=False)
    encrypted_notes: Mapped[Optional[str]] = mapped_column(Text)
    
    # Encryption metadata
    encryption_key_id: Mapped[str] = mapped_column(String(255), nullable=False)
    encryption_algorithm: Mapped[str] = mapped_column(String(50), default="AES-256-GCM", nullable=False)
    
    # Organization
    category_id: Mapped[Optional[int]] = mapped_column(ForeignKey("password_categories.id"))
    tags: Mapped[Optional[list[str]]] = mapped_column(Text)  # JSON array as text
    
    # Security features
    is_favorite: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_shared: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    requires_master_password: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Password metadata
    password_strength_score: Mapped[Optional[int]] = mapped_column()  # 0-100
    password_last_changed: Mapped[Optional[datetime]] = mapped_column(DateTime)
    password_expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Access tracking
    last_accessed_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    access_count: Mapped[int] = mapped_column(default=0, nullable=False)
    
    # Ownership
    owner_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships
    owner: Mapped["User"] = relationship("User", back_populates="password_entries")
    category: Mapped[Optional["PasswordCategory"]] = relationship(
        "PasswordCategory", 
        back_populates="password_entries"
    )
    
    def __repr__(self) -> str:
        return f"<PasswordEntry(id={self.id}, title='{self.title}', owner_id={self.owner_id})>"
    
    @property
    def is_password_expired(self) -> bool:
        """Check if password is expired."""
        if not self.password_expires_at:
            return False
        return datetime.utcnow() > self.password_expires_at
    
    @property
    def days_until_expiration(self) -> Optional[int]:
        """Get days until password expiration."""
        if not self.password_expires_at:
            return None
        
        delta = self.password_expires_at - datetime.utcnow()
        return max(0, delta.days)
