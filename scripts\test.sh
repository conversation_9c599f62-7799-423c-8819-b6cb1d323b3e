#!/bin/bash

# Lonors AI Agent Platform - Testing Script
# This script runs comprehensive tests with coverage reporting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the project root
if [ ! -f "docker-compose.yml" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Parse command line arguments
BACKEND_ONLY=false
FRONTEND_ONLY=false
COVERAGE=true
VERBOSE=false
FAST=false
INTEGRATION=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --backend-only)
            BACKEND_ONLY=true
            shift
            ;;
        --frontend-only)
            FRONTEND_ONLY=true
            shift
            ;;
        --no-coverage)
            COVERAGE=false
            shift
            ;;
        --verbose|-v)
            VERBOSE=true
            shift
            ;;
        --fast)
            FAST=true
            shift
            ;;
        --integration)
            INTEGRATION=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --backend-only      Run only backend tests"
            echo "  --frontend-only     Run only frontend tests"
            echo "  --no-coverage       Skip coverage reporting"
            echo "  --verbose, -v       Verbose output"
            echo "  --fast              Skip slow tests"
            echo "  --integration       Run integration tests"
            echo "  -h, --help          Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Function to run backend tests
run_backend_tests() {
    print_status "Running backend tests..."
    
    cd backend
    
    # Ensure dependencies are installed
    if [ ! -d ".venv" ]; then
        print_status "Installing backend dependencies..."
        uv sync --dev
    fi
    
    # Build pytest command
    PYTEST_CMD="uv run pytest"
    
    if [ "$VERBOSE" = true ]; then
        PYTEST_CMD="$PYTEST_CMD -v"
    fi
    
    if [ "$COVERAGE" = true ]; then
        PYTEST_CMD="$PYTEST_CMD --cov=app --cov-report=term-missing --cov-report=html --cov-report=xml"
    fi
    
    if [ "$FAST" = true ]; then
        PYTEST_CMD="$PYTEST_CMD -m 'not slow'"
    fi
    
    if [ "$INTEGRATION" = true ]; then
        PYTEST_CMD="$PYTEST_CMD -m integration"
    else
        PYTEST_CMD="$PYTEST_CMD -m 'not integration'"
    fi
    
    # Run tests
    if $PYTEST_CMD; then
        print_success "Backend tests passed!"
        
        if [ "$COVERAGE" = true ]; then
            print_status "Coverage report generated:"
            echo "  HTML: backend/htmlcov/index.html"
            echo "  XML:  backend/coverage.xml"
        fi
    else
        print_error "Backend tests failed!"
        cd ..
        exit 1
    fi
    
    cd ..
}

# Function to run backend linting
run_backend_linting() {
    print_status "Running backend code quality checks..."
    
    cd backend
    
    # Black formatting check
    print_status "Checking code formatting with Black..."
    if uv run black --check .; then
        print_success "Code formatting is correct"
    else
        print_error "Code formatting issues found. Run 'uv run black .' to fix"
        cd ..
        exit 1
    fi
    
    # isort import sorting check
    print_status "Checking import sorting with isort..."
    if uv run isort --check-only .; then
        print_success "Import sorting is correct"
    else
        print_error "Import sorting issues found. Run 'uv run isort .' to fix"
        cd ..
        exit 1
    fi
    
    # Flake8 linting
    print_status "Running flake8 linting..."
    if uv run flake8 .; then
        print_success "No linting issues found"
    else
        print_error "Linting issues found"
        cd ..
        exit 1
    fi
    
    # MyPy type checking
    print_status "Running MyPy type checking..."
    if uv run mypy .; then
        print_success "Type checking passed"
    else
        print_error "Type checking failed"
        cd ..
        exit 1
    fi
    
    # Bandit security check
    print_status "Running Bandit security check..."
    if uv run bandit -r app/; then
        print_success "Security check passed"
    else
        print_error "Security issues found"
        cd ..
        exit 1
    fi
    
    # Safety dependency check
    print_status "Running Safety dependency check..."
    if uv run safety check; then
        print_success "Dependency security check passed"
    else
        print_warning "Dependency security issues found"
        # Don't fail on safety issues in development
    fi
    
    cd ..
}

# Function to run frontend tests
run_frontend_tests() {
    print_status "Running frontend tests..."
    
    cd frontend
    
    # Ensure dependencies are installed
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies..."
        pnpm install
    fi
    
    # Build test command
    TEST_CMD="pnpm test"
    
    if [ "$COVERAGE" = true ]; then
        TEST_CMD="pnpm test:coverage"
    fi
    
    if [ "$VERBOSE" = true ]; then
        TEST_CMD="$TEST_CMD --verbose"
    fi
    
    # Run tests
    if $TEST_CMD; then
        print_success "Frontend tests passed!"
        
        if [ "$COVERAGE" = true ]; then
            print_status "Coverage report generated:"
            echo "  HTML: frontend/coverage/lcov-report/index.html"
            echo "  LCOV: frontend/coverage/lcov.info"
        fi
    else
        print_error "Frontend tests failed!"
        cd ..
        exit 1
    fi
    
    cd ..
}

# Function to run frontend linting
run_frontend_linting() {
    print_status "Running frontend code quality checks..."
    
    cd frontend
    
    # ESLint
    print_status "Running ESLint..."
    if pnpm lint; then
        print_success "ESLint passed"
    else
        print_error "ESLint issues found"
        cd ..
        exit 1
    fi
    
    # Prettier formatting check
    print_status "Checking code formatting with Prettier..."
    if pnpm format:check; then
        print_success "Code formatting is correct"
    else
        print_error "Code formatting issues found. Run 'pnpm format' to fix"
        cd ..
        exit 1
    fi
    
    # TypeScript type checking
    print_status "Running TypeScript type checking..."
    if pnpm type-check; then
        print_success "Type checking passed"
    else
        print_error "Type checking failed"
        cd ..
        exit 1
    fi
    
    cd ..
}

# Function to run integration tests
run_integration_tests() {
    print_status "Running integration tests..."
    
    # Start test database
    print_status "Starting test database..."
    docker-compose -f docker-compose.test.yml up -d postgres-test
    
    # Wait for database
    sleep 5
    
    # Run integration tests
    cd backend
    if uv run pytest tests/test_integration/ -v; then
        print_success "Integration tests passed!"
    else
        print_error "Integration tests failed!"
        cd ..
        docker-compose -f docker-compose.test.yml down
        exit 1
    fi
    cd ..
    
    # Cleanup
    docker-compose -f docker-compose.test.yml down
}

# Function to generate test report
generate_test_report() {
    print_status "Generating test report..."
    
    # Create reports directory
    mkdir -p reports
    
    # Combine coverage reports if both backend and frontend were tested
    if [ "$BACKEND_ONLY" = false ] && [ "$FRONTEND_ONLY" = false ] && [ "$COVERAGE" = true ]; then
        print_status "Combining coverage reports..."
        
        # This would require a tool like codecov or a custom script
        # For now, just copy individual reports
        cp backend/coverage.xml reports/backend-coverage.xml 2>/dev/null || true
        cp frontend/coverage/lcov.info reports/frontend-coverage.lcov 2>/dev/null || true
    fi
    
    print_success "Test report generated in reports/ directory"
}

# Main execution
echo
print_status "Running Lonors AI Agent Platform Tests"
echo "======================================"

# Run backend tests
if [ "$FRONTEND_ONLY" = false ]; then
    run_backend_linting
    run_backend_tests
fi

# Run frontend tests
if [ "$BACKEND_ONLY" = false ]; then
    run_frontend_linting
    run_frontend_tests
fi

# Run integration tests if requested
if [ "$INTEGRATION" = true ]; then
    run_integration_tests
fi

# Generate test report
if [ "$COVERAGE" = true ]; then
    generate_test_report
fi

echo
print_success "🎉 All tests passed successfully!"
echo

# Show coverage summary
if [ "$COVERAGE" = true ]; then
    echo "Coverage Reports:"
    if [ "$FRONTEND_ONLY" = false ]; then
        echo "  Backend:  backend/htmlcov/index.html"
    fi
    if [ "$BACKEND_ONLY" = false ]; then
        echo "  Frontend: frontend/coverage/lcov-report/index.html"
    fi
    echo
fi

print_status "Test execution completed successfully"
