#!/usr/bin/env python3
"""Validation script for RAG implementation."""

import asyncio
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.services.document_processor import DocumentProcessor
from app.services.embedding_service import EmbeddingService
from app.core.config import get_settings


async def validate_document_processor():
    """Validate document processor functionality."""
    print("🔧 Testing Document Processor...")
    
    processor = DocumentProcessor()
    
    # Test text cleaning
    dirty_text = "  This   has\t\texcessive\n\n\nwhitespace  \r\n"
    cleaned = processor._clean_text(dirty_text)
    assert cleaned == "This has excessive whitespace", f"Expected clean text, got: {cleaned}"
    print("✅ Text cleaning works")
    
    # Test chunking
    text = "This is a test document. " * 50  # Create longer text
    chunks = processor.create_chunks(text)
    assert len(chunks) > 0, "Should create chunks"
    assert all("content" in chunk for chunk in chunks), "All chunks should have content"
    print(f"✅ Text chunking works ({len(chunks)} chunks created)")
    
    # Test file validation
    try:
        doc_type, mime_type = await processor.validate_file(
            b"This is test content", "test.txt"
        )
        assert doc_type.value == "txt", f"Expected txt type, got: {doc_type}"
        print("✅ File validation works")
    except Exception as e:
        print(f"❌ File validation failed: {e}")
        return False
    
    print("✅ Document Processor validation passed!")
    return True


async def validate_embedding_service():
    """Validate embedding service functionality."""
    print("\n🤖 Testing Embedding Service...")
    
    # Use a lightweight model for testing
    embedding_service = EmbeddingService(model_name="all-MiniLM-L6-v2")
    
    try:
        # Test initialization
        await embedding_service.initialize()
        assert embedding_service.is_initialized, "Service should be initialized"
        assert embedding_service.dimension > 0, "Should have valid dimension"
        print(f"✅ Embedding service initialized (dimension: {embedding_service.dimension})")
        
        # Test single embedding
        embedding = await embedding_service.generate_embedding("This is a test sentence.")
        assert isinstance(embedding, list), "Embedding should be a list"
        assert len(embedding) == embedding_service.dimension, "Embedding should have correct dimension"
        print("✅ Single embedding generation works")
        
        # Test batch embeddings
        texts = ["First sentence.", "Second sentence.", "Third sentence."]
        embeddings = await embedding_service.generate_embeddings_batch(texts)
        assert len(embeddings) == 3, "Should generate 3 embeddings"
        assert all(len(emb) == embedding_service.dimension for emb in embeddings), "All embeddings should have correct dimension"
        print("✅ Batch embedding generation works")
        
        # Test similarity calculation
        similarity = embedding_service.calculate_similarity(embeddings[0], embeddings[0])
        assert similarity == 1.0, f"Self-similarity should be 1.0, got: {similarity}"
        print("✅ Similarity calculation works")
        
    except Exception as e:
        print(f"❌ Embedding service failed: {e}")
        return False
    
    print("✅ Embedding Service validation passed!")
    return True


async def validate_configuration():
    """Validate configuration settings."""
    print("\n⚙️  Testing Configuration...")
    
    settings = get_settings()
    
    # Check required settings
    required_settings = [
        "DEFAULT_EMBEDDING_MODEL",
        "QDRANT_HOST",
        "QDRANT_PORT",
        "EMBEDDING_BATCH_SIZE",
    ]
    
    for setting in required_settings:
        value = getattr(settings, setting, None)
        assert value is not None, f"Setting {setting} should be defined"
        print(f"✅ {setting}: {value}")
    
    print("✅ Configuration validation passed!")
    return True


async def main():
    """Run all validations."""
    print("🚀 Starting RAG Implementation Validation")
    print("=" * 50)
    
    try:
        # Validate configuration
        config_ok = await validate_configuration()
        
        # Validate document processor
        processor_ok = await validate_document_processor()
        
        # Validate embedding service (this might take a while on first run)
        print("\n⏳ Downloading embedding model (this may take a few minutes on first run)...")
        embedding_ok = await validate_embedding_service()
        
        # Summary
        print("\n" + "=" * 50)
        if config_ok and processor_ok and embedding_ok:
            print("🎉 All validations passed! RAG implementation is working correctly.")
            print("\n📋 Next steps:")
            print("1. Start the development environment: ./scripts/dev.sh")
            print("2. Upload a document via the API")
            print("3. Test semantic search functionality")
            return 0
        else:
            print("❌ Some validations failed. Please check the errors above.")
            return 1
            
    except Exception as e:
        print(f"\n💥 Validation failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
