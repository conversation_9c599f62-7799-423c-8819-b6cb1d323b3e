"""Authentication-related Pydantic schemas."""

from typing import Optional

from pydantic import BaseModel, EmailStr, Field, validator


class Token(BaseModel):
    """Token response schema."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int  # Seconds until expiration


class TokenData(BaseModel):
    """Token data schema for internal use."""
    user_id: Optional[int] = None
    username: Optional[str] = None
    permissions: list[str] = []


class UserLogin(BaseModel):
    """User login request schema."""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=1, description="User password")
    remember_me: bool = Field(default=False, description="Remember login for extended period")
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "securepassword123",
                "remember_me": False
            }
        }


class UserRegister(BaseModel):
    """User registration request schema."""
    email: EmailStr = Field(..., description="User email address")
    username: str = Field(
        ..., 
        min_length=3, 
        max_length=50, 
        regex="^[a-zA-Z0-9_-]+$",
        description="Username (alphanumeric, underscore, hyphen only)"
    )
    password: str = Field(
        ..., 
        min_length=8, 
        description="Password (minimum 8 characters)"
    )
    full_name: Optional[str] = Field(
        None, 
        max_length=255, 
        description="Full name"
    )
    
    @validator("password")
    def validate_password_strength(cls, v):
        """Validate password strength."""
        from app.core.security import SecurityValidator
        
        is_valid, issues = SecurityValidator.validate_password_strength(v)
        if not is_valid:
            raise ValueError(f"Password validation failed: {'; '.join(issues)}")
        return v
    
    @validator("username")
    def validate_username(cls, v):
        """Validate username format."""
        if not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError("Username can only contain letters, numbers, underscores, and hyphens")
        return v.lower()
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "username": "newuser123",
                "password": "SecurePass123!",
                "full_name": "New User"
            }
        }


class PasswordResetRequest(BaseModel):
    """Password reset request schema."""
    email: EmailStr = Field(..., description="Email address for password reset")
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>"
            }
        }


class PasswordReset(BaseModel):
    """Password reset schema."""
    token: str = Field(..., description="Password reset token")
    new_password: str = Field(
        ..., 
        min_length=8, 
        description="New password (minimum 8 characters)"
    )
    
    @validator("new_password")
    def validate_password_strength(cls, v):
        """Validate password strength."""
        from app.core.security import SecurityValidator
        
        is_valid, issues = SecurityValidator.validate_password_strength(v)
        if not is_valid:
            raise ValueError(f"Password validation failed: {'; '.join(issues)}")
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "new_password": "NewSecurePass123!"
            }
        }


class RefreshToken(BaseModel):
    """Refresh token request schema."""
    refresh_token: str = Field(..., description="Refresh token")
    
    class Config:
        schema_extra = {
            "example": {
                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            }
        }


class EmailVerification(BaseModel):
    """Email verification schema."""
    token: str = Field(..., description="Email verification token")
    
    class Config:
        schema_extra = {
            "example": {
                "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            }
        }


class ChangePassword(BaseModel):
    """Change password schema."""
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(
        ..., 
        min_length=8, 
        description="New password (minimum 8 characters)"
    )
    
    @validator("new_password")
    def validate_password_strength(cls, v):
        """Validate password strength."""
        from app.core.security import SecurityValidator
        
        is_valid, issues = SecurityValidator.validate_password_strength(v)
        if not is_valid:
            raise ValueError(f"Password validation failed: {'; '.join(issues)}")
        return v
    
    @validator("new_password")
    def passwords_must_be_different(cls, v, values):
        """Ensure new password is different from current."""
        if "current_password" in values and v == values["current_password"]:
            raise ValueError("New password must be different from current password")
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "current_password": "OldPassword123!",
                "new_password": "NewSecurePass123!"
            }
        }
