# Environment Configuration
ENVIRONMENT=development

# Database Configuration
DATABASE_URL=postgresql://lonors:lonors_dev_password@localhost:5432/lonors
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Vector Database Configuration
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=
QDRANT_COLLECTION_NAME=lonors_vectors

# Cache Configuration
REDIS_URL=redis://localhost:6379
REDIS_DB=0
REDIS_PASSWORD=

# Graph Database Configuration
NEO4J_URL=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=lonors_dev_password

# Authentication Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# OAuth Configuration
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

# AI/ML Configuration
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
HUGGINGFACE_API_KEY=
OLLAMA_BASE_URL=http://localhost:11434

# File Storage Configuration
STORAGE_TYPE=local  # local, s3, minio
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
S3_BUCKET_NAME=lonors-storage

MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=
MINIO_SECRET_KEY=
MINIO_BUCKET_NAME=lonors-storage

# Monitoring Configuration
SENTRY_DSN=
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=8000

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/app.log

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Web Scraping Configuration
SCRAPING_DELAY=1
SCRAPING_CONCURRENT_REQUESTS=16
SCRAPING_USER_AGENT=Lonors-Bot/1.0

# Security Configuration
CORS_ORIGINS=["http://localhost:5500", "http://localhost:3000"]
ALLOWED_HOSTS=["localhost", "127.0.0.1"]

# Feature Flags
ENABLE_RAG=true
ENABLE_VOICE_TRANSCRIPTION=true
ENABLE_WEB_CRAWLING=true
ENABLE_KNOWLEDGE_GRAPH=true
ENABLE_PASSWORD_MANAGER=true

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Model Configuration
DEFAULT_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
DEFAULT_LLM_MODEL=gpt-3.5-turbo
WHISPER_MODEL=base

# Development Configuration
DEBUG=true
RELOAD=true
WORKERS=1
