"""Tests for authentication API endpoints."""

import pytest
from httpx import <PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User
from tests.conftest import UserFactory


class TestUserRegistration:
    """Test user registration endpoint."""
    
    @pytest.mark.asyncio
    async def test_register_user_success(self, async_client: AsyncClient):
        """Test successful user registration."""
        user_data = {
            "email": "<EMAIL>",
            "username": "newuser",
            "password": "SecurePass123!",
            "full_name": "New User",
        }
        
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 201
        data = response.json()
        
        assert data["email"] == user_data["email"]
        assert data["username"] == user_data["username"]
        assert data["full_name"] == user_data["full_name"]
        assert data["is_active"] is True
        assert data["is_verified"] is False  # Should require email verification
        assert "id" in data
        assert "created_at" in data
        assert "hashed_password" not in data  # Should not expose password
    
    @pytest.mark.asyncio
    async def test_register_duplicate_email(
        self, 
        async_client: AsyncClient, 
        test_user: User
    ):
        """Test registration with duplicate email."""
        user_data = {
            "email": test_user.email,  # Use existing email
            "username": "differentuser",
            "password": "SecurePass123!",
        }
        
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 409
        data = response.json()
        assert "Email already registered" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_register_duplicate_username(
        self, 
        async_client: AsyncClient, 
        test_user: User
    ):
        """Test registration with duplicate username."""
        user_data = {
            "email": "<EMAIL>",
            "username": test_user.username,  # Use existing username
            "password": "SecurePass123!",
        }
        
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 409
        data = response.json()
        assert "Username already taken" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_register_invalid_email(self, async_client: AsyncClient):
        """Test registration with invalid email."""
        user_data = {
            "email": "invalid-email",
            "username": "testuser",
            "password": "SecurePass123!",
        }
        
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_register_weak_password(self, async_client: AsyncClient):
        """Test registration with weak password."""
        user_data = {
            "email": "<EMAIL>",
            "username": "testuser",
            "password": "weak",  # Too weak
        }
        
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 422
        data = response.json()
        assert "Password validation failed" in str(data)


class TestUserLogin:
    """Test user login endpoint."""
    
    @pytest.mark.asyncio
    async def test_login_success(self, async_client: AsyncClient, test_user: User):
        """Test successful login."""
        login_data = {
            "email": test_user.email,
            "password": "testpassword123",  # From test_user fixture
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        assert "expires_in" in data
        assert isinstance(data["expires_in"], int)
    
    @pytest.mark.asyncio
    async def test_login_invalid_email(self, async_client: AsyncClient):
        """Test login with invalid email."""
        login_data = {
            "email": "<EMAIL>",
            "password": "anypassword",
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert "Invalid email or password" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_login_invalid_password(self, async_client: AsyncClient, test_user: User):
        """Test login with invalid password."""
        login_data = {
            "email": test_user.email,
            "password": "wrongpassword",
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert "Invalid email or password" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_login_inactive_user(
        self, 
        async_client: AsyncClient, 
        db_session: AsyncSession,
        user_factory: UserFactory
    ):
        """Test login with inactive user."""
        # Create inactive user
        inactive_user = await user_factory.create(
            db_session, 
            email="<EMAIL>",
            username="inactive",
            is_active=False
        )
        
        login_data = {
            "email": inactive_user.email,
            "password": "TestPassword123!",
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert "Account is disabled" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_login_remember_me(self, async_client: AsyncClient, test_user: User):
        """Test login with remember me option."""
        login_data = {
            "email": test_user.email,
            "password": "testpassword123",
            "remember_me": True,
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # Should have longer expiration time
        assert data["expires_in"] > 3600  # More than 1 hour


class TestTokenRefresh:
    """Test token refresh endpoint."""
    
    @pytest.mark.asyncio
    async def test_refresh_token_success(
        self, 
        async_client: AsyncClient, 
        test_user: User
    ):
        """Test successful token refresh."""
        # First, login to get tokens
        login_data = {
            "email": test_user.email,
            "password": "testpassword123",
        }
        
        login_response = await async_client.post("/api/v1/auth/login", json=login_data)
        login_data = login_response.json()
        
        # Use refresh token
        refresh_data = {
            "refresh_token": login_data["refresh_token"]
        }
        
        response = await async_client.post("/api/v1/auth/refresh", json=refresh_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        
        # New tokens should be different
        assert data["access_token"] != login_data["access_token"]
        assert data["refresh_token"] != login_data["refresh_token"]
    
    @pytest.mark.asyncio
    async def test_refresh_invalid_token(self, async_client: AsyncClient):
        """Test refresh with invalid token."""
        refresh_data = {
            "refresh_token": "invalid_token"
        }
        
        response = await async_client.post("/api/v1/auth/refresh", json=refresh_data)
        
        assert response.status_code == 401


class TestCurrentUser:
    """Test current user endpoint."""
    
    @pytest.mark.asyncio
    async def test_get_current_user_success(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test getting current user information."""
        response = await async_client.get("/api/v1/auth/me", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "id" in data
        assert "email" in data
        assert "username" in data
        assert "permissions" in data
        assert "hashed_password" not in data  # Should not expose password
    
    @pytest.mark.asyncio
    async def test_get_current_user_unauthorized(self, async_client: AsyncClient):
        """Test getting current user without authentication."""
        response = await async_client.get("/api/v1/auth/me")
        
        assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_get_current_user_invalid_token(self, async_client: AsyncClient):
        """Test getting current user with invalid token."""
        headers = {"Authorization": "Bearer invalid_token"}
        response = await async_client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 401


class TestPasswordChange:
    """Test password change endpoint."""
    
    @pytest.mark.asyncio
    async def test_change_password_success(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test successful password change."""
        password_data = {
            "current_password": "testpassword123",
            "new_password": "NewSecurePass123!",
        }
        
        response = await async_client.post(
            "/api/v1/auth/change-password", 
            json=password_data, 
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "Password changed successfully" in data["message"]
    
    @pytest.mark.asyncio
    async def test_change_password_wrong_current(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test password change with wrong current password."""
        password_data = {
            "current_password": "wrongpassword",
            "new_password": "NewSecurePass123!",
        }
        
        response = await async_client.post(
            "/api/v1/auth/change-password", 
            json=password_data, 
            headers=auth_headers
        )
        
        assert response.status_code == 401
        data = response.json()
        assert "Current password is incorrect" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_change_password_unauthorized(self, async_client: AsyncClient):
        """Test password change without authentication."""
        password_data = {
            "current_password": "testpassword123",
            "new_password": "NewSecurePass123!",
        }
        
        response = await async_client.post(
            "/api/v1/auth/change-password", 
            json=password_data
        )
        
        assert response.status_code == 401
