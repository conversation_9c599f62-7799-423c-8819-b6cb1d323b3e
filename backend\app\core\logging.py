"""Structured logging configuration for the application."""

import logging
import sys
import uuid
from contextvars import Context<PERSON><PERSON>
from typing import Any, Dict, Optional

import structlog
from structlog.types import EventDict, Processor

from app.core.config import get_settings

settings = get_settings()

# Context variable for correlation ID
correlation_id_var: ContextVar[Optional[str]] = ContextVar("correlation_id", default=None)


def add_correlation_id(logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
    """Add correlation ID to log entries."""
    correlation_id = correlation_id_var.get()
    if correlation_id:
        event_dict["correlation_id"] = correlation_id
    return event_dict


def add_service_info(logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
    """Add service information to log entries."""
    event_dict.update({
        "service": "lonors-backend",
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
    })
    return event_dict


def add_timestamp(logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
    """Add ISO timestamp to log entries."""
    import datetime
    event_dict["timestamp"] = datetime.datetime.utcnow().isoformat() + "Z"
    return event_dict


def filter_sensitive_data(logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
    """Filter sensitive data from log entries."""
    sensitive_keys = {
        "password", "token", "secret", "key", "authorization", 
        "cookie", "session", "api_key", "access_token", "refresh_token"
    }
    
    def _filter_dict(data: Dict[str, Any]) -> Dict[str, Any]:
        filtered = {}
        for key, value in data.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                filtered[key] = "[REDACTED]"
            elif isinstance(value, dict):
                filtered[key] = _filter_dict(value)
            elif isinstance(value, list):
                filtered[key] = [_filter_dict(item) if isinstance(item, dict) else item for item in value]
            else:
                filtered[key] = value
        return filtered
    
    # Filter the entire event dict
    return _filter_dict(event_dict)


def setup_logging() -> None:
    """Configure structured logging for the application."""
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.LOG_LEVEL.upper()),
    )
    
    # Configure structlog processors
    processors: list[Processor] = [
        structlog.contextvars.merge_contextvars,
        add_correlation_id,
        add_service_info,
        add_timestamp,
        filter_sensitive_data,
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
    ]
    
    if settings.LOG_FORMAT.lower() == "json":
        processors.extend([
            structlog.processors.dict_tracebacks,
            structlog.processors.JSONRenderer(),
        ])
    else:
        processors.extend([
            structlog.processors.ExceptionPrettyPrinter(),
            structlog.dev.ConsoleRenderer(colors=True),
        ])
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, settings.LOG_LEVEL.upper())
        ),
        logger_factory=structlog.PrintLoggerFactory(),
        cache_logger_on_first_use=True,
    )


def get_correlation_id() -> str:
    """Get or generate correlation ID for request tracking."""
    correlation_id = correlation_id_var.get()
    if not correlation_id:
        correlation_id = str(uuid.uuid4())
        correlation_id_var.set(correlation_id)
    return correlation_id


def set_correlation_id(correlation_id: str) -> None:
    """Set correlation ID for request tracking."""
    correlation_id_var.set(correlation_id)


class LoggerMixin:
    """Mixin class to add structured logging to any class."""
    
    @property
    def logger(self) -> structlog.BoundLogger:
        """Get a bound logger for this class."""
        return structlog.get_logger(self.__class__.__name__)


def log_function_call(func_name: str, **kwargs) -> None:
    """Log function call with parameters."""
    logger = structlog.get_logger()
    logger.info(
        "Function called",
        function=func_name,
        parameters=kwargs,
    )


def log_function_result(func_name: str, result: Any = None, duration_ms: Optional[float] = None) -> None:
    """Log function result and execution time."""
    logger = structlog.get_logger()
    log_data = {
        "function": func_name,
        "status": "completed",
    }
    
    if duration_ms is not None:
        log_data["duration_ms"] = round(duration_ms, 2)
    
    if result is not None:
        log_data["result_type"] = type(result).__name__
    
    logger.info("Function completed", **log_data)


def log_error(error: Exception, context: Optional[Dict[str, Any]] = None) -> None:
    """Log error with context information."""
    logger = structlog.get_logger()
    log_data = {
        "error_type": type(error).__name__,
        "error_message": str(error),
    }
    
    if context:
        log_data["context"] = context
    
    logger.error("Error occurred", **log_data, exc_info=True)


def log_performance_metric(metric_name: str, value: float, unit: str = "ms", **tags) -> None:
    """Log performance metrics."""
    logger = structlog.get_logger()
    logger.info(
        "Performance metric",
        metric_name=metric_name,
        value=value,
        unit=unit,
        **tags,
    )


def log_business_event(event_name: str, **data) -> None:
    """Log business events for analytics."""
    logger = structlog.get_logger()
    logger.info(
        "Business event",
        event_name=event_name,
        event_data=data,
    )


def log_security_event(event_type: str, severity: str = "medium", **details) -> None:
    """Log security-related events."""
    logger = structlog.get_logger()
    logger.warning(
        "Security event",
        event_type=event_type,
        severity=severity,
        details=details,
    )


def log_api_request(method: str, path: str, status_code: int, duration_ms: float, **metadata) -> None:
    """Log API request details."""
    logger = structlog.get_logger()
    logger.info(
        "API request",
        method=method,
        path=path,
        status_code=status_code,
        duration_ms=round(duration_ms, 2),
        **metadata,
    )


def log_database_operation(operation: str, table: str, duration_ms: float, affected_rows: int = 0) -> None:
    """Log database operations."""
    logger = structlog.get_logger()
    logger.info(
        "Database operation",
        operation=operation,
        table=table,
        duration_ms=round(duration_ms, 2),
        affected_rows=affected_rows,
    )
