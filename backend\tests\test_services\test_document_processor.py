"""Tests for document processor service."""

import tempfile
from pathlib import Path
from unittest.mock import patch

import pytest

from app.core.exceptions import ProcessingError, ValidationError
from app.models.document import DocumentTypeEnum
from app.services.document_processor import DocumentProcessor


class TestDocumentProcessor:
    """Test document processor functionality."""
    
    @pytest.fixture
    def processor(self):
        """Create document processor with temporary storage."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield DocumentProcessor(storage_path=temp_dir)
    
    @pytest.fixture
    def sample_text_content(self):
        """Sample text content for testing."""
        return b"This is a sample text file for testing document processing."
    
    @pytest.fixture
    def sample_pdf_content(self):
        """Sample PDF content (minimal PDF structure)."""
        return b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj
4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Hello World) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""

    @pytest.mark.asyncio
    async def test_validate_text_file_success(self, processor, sample_text_content):
        """Test successful text file validation."""
        doc_type, mime_type = await processor.validate_file(
            sample_text_content, "test.txt"
        )
        
        assert doc_type == DocumentTypeEnum.TXT
        assert mime_type == "text/plain"
    
    @pytest.mark.asyncio
    async def test_validate_empty_file_fails(self, processor):
        """Test validation fails for empty file."""
        with pytest.raises(ValidationError, match="File is empty"):
            await processor.validate_file(b"", "test.txt")
    
    @pytest.mark.asyncio
    async def test_validate_oversized_file_fails(self, processor):
        """Test validation fails for oversized file."""
        large_content = b"x" * (101 * 1024 * 1024)  # 101MB
        
        with pytest.raises(ValidationError, match="File too large"):
            await processor.validate_file(large_content, "test.txt")
    
    @pytest.mark.asyncio
    async def test_validate_unsupported_file_type_fails(self, processor):
        """Test validation fails for unsupported file type."""
        with pytest.raises(ValidationError, match="Unsupported file type"):
            await processor.validate_file(b"fake content", "test.exe")
    
    @pytest.mark.asyncio
    async def test_validate_suspicious_content_fails(self, processor):
        """Test validation fails for suspicious content."""
        suspicious_content = b"<script>alert('xss')</script>"
        
        with pytest.raises(ValidationError, match="suspicious content"):
            await processor.validate_file(suspicious_content, "test.txt")
    
    @pytest.mark.asyncio
    async def test_save_file_success(self, processor, sample_text_content):
        """Test successful file saving."""
        file_path, content_hash = await processor.save_file(
            sample_text_content, "test.txt", user_id=1
        )
        
        assert Path(file_path).exists()
        assert len(content_hash) == 64  # SHA-256 hash length
        
        # Verify file content
        with open(file_path, 'rb') as f:
            saved_content = f.read()
        assert saved_content == sample_text_content
    
    @pytest.mark.asyncio
    async def test_extract_text_from_txt_file(self, processor, sample_text_content):
        """Test text extraction from TXT file."""
        # Save file first
        file_path, _ = await processor.save_file(
            sample_text_content, "test.txt", user_id=1
        )
        
        # Extract text
        extracted_text = await processor.extract_text(file_path, DocumentTypeEnum.TXT)
        
        assert extracted_text == sample_text_content.decode('utf-8')
    
    @pytest.mark.asyncio
    async def test_extract_text_unsupported_type_fails(self, processor):
        """Test text extraction fails for unsupported type."""
        with pytest.raises(ProcessingError, match="Unsupported document type"):
            await processor.extract_text("/fake/path", DocumentTypeEnum.JSON)
    
    def test_create_chunks_basic(self, processor):
        """Test basic text chunking."""
        text = "This is a test document. " * 100  # Create longer text
        
        chunks = processor.create_chunks(text)
        
        assert len(chunks) > 1
        assert all(chunk["character_count"] >= processor.min_chunk_size for chunk in chunks)
        assert all("content" in chunk for chunk in chunks)
        assert all("chunk_index" in chunk for chunk in chunks)
    
    def test_create_chunks_with_metadata(self, processor):
        """Test text chunking with metadata."""
        text = "This is a test document. " * 50
        metadata = {"document_id": 123, "page_number": 1}
        
        chunks = processor.create_chunks(text, metadata)
        
        assert all(chunk["document_id"] == 123 for chunk in chunks)
        assert all(chunk["page_number"] == 1 for chunk in chunks)
    
    def test_create_chunks_empty_text(self, processor):
        """Test chunking with empty text."""
        chunks = processor.create_chunks("")
        assert chunks == []
        
        chunks = processor.create_chunks("   ")
        assert chunks == []
    
    def test_create_chunks_short_text(self, processor):
        """Test chunking with text shorter than minimum chunk size."""
        short_text = "Short text."
        
        chunks = processor.create_chunks(short_text)
        
        # Should return empty list if text is too short
        assert chunks == []
    
    def test_clean_text(self, processor):
        """Test text cleaning functionality."""
        dirty_text = "  This   has\t\texcessive\n\n\nwhitespace  \r\n"
        
        cleaned = processor._clean_text(dirty_text)
        
        assert cleaned == "This has excessive whitespace"
    
    def test_find_sentence_boundary(self, processor):
        """Test sentence boundary detection."""
        text = "First sentence. Second sentence! Third sentence?"
        
        # Should find boundary after first sentence
        boundary = processor._find_sentence_boundary(text, 0, 20)
        assert boundary == 16  # After "First sentence."
    
    def test_estimate_token_count(self, processor):
        """Test token count estimation."""
        text = "This is a test sentence with multiple words."
        
        token_count = processor._estimate_token_count(text)
        
        # Should be roughly text length / 4
        expected = len(text) // 4
        assert abs(token_count - expected) <= 1
    
    @pytest.mark.asyncio
    async def test_validate_file_by_extension_fallback(self, processor):
        """Test file validation falls back to extension when MIME detection fails."""
        with patch('magic.from_buffer', side_effect=Exception("MIME detection failed")):
            doc_type, mime_type = await processor.validate_file(
                b"test content", "test.txt"
            )
            
            assert doc_type == DocumentTypeEnum.TXT
            assert mime_type == "text/plain"
    
    def test_chunking_configuration(self, processor):
        """Test chunking configuration parameters."""
        # Test default configuration
        assert processor.chunk_size == 1000
        assert processor.chunk_overlap == 200
        assert processor.min_chunk_size == 100
        
        # Test configuration affects chunking
        text = "A" * 2000  # 2000 character text
        chunks = processor.create_chunks(text)
        
        # Should create multiple chunks
        assert len(chunks) >= 2
        
        # First chunk should be around chunk_size
        assert len(chunks[0]["content"]) <= processor.chunk_size + 50  # Some tolerance
    
    @pytest.mark.asyncio
    async def test_file_path_sanitization(self, processor, sample_text_content):
        """Test file path sanitization for security."""
        dangerous_filename = "../../../etc/passwd"
        
        file_path, _ = await processor.save_file(
            sample_text_content, dangerous_filename, user_id=1
        )
        
        # Should not contain path traversal
        assert "../" not in file_path
        assert "etc/passwd" not in file_path
        
        # Should be in user directory
        assert "/1/" in file_path or "\\1\\" in file_path  # User ID directory
