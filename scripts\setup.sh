#!/bin/bash

# Lonors AI Agent Platform - Development Setup Script
# This script sets up the complete development environment

set -e

echo "🚀 Setting up Lonors AI Agent Platform Development Environment"
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on supported OS
check_os() {
    print_status "Checking operating system..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        print_success "Linux detected"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        print_success "macOS detected"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
        print_success "Windows detected"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    print_success "Docker found"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    print_success "Docker Compose found"
    
    # Check Git
    if ! command -v git &> /dev/null; then
        print_error "Git is not installed. Please install Git first."
        exit 1
    fi
    print_success "Git found"
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_warning "Python 3 not found. Installing via package manager..."
        install_python
    else
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
        if [[ $(echo "$PYTHON_VERSION >= 3.11" | bc -l) -eq 1 ]]; then
            print_success "Python $PYTHON_VERSION found"
        else
            print_error "Python 3.11+ required, found $PYTHON_VERSION"
            exit 1
        fi
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_warning "Node.js not found. Installing via package manager..."
        install_nodejs
    else
        NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [[ $NODE_VERSION -ge 18 ]]; then
            print_success "Node.js v$NODE_VERSION found"
        else
            print_error "Node.js 18+ required, found v$NODE_VERSION"
            exit 1
        fi
    fi
}

# Install Python
install_python() {
    case $OS in
        "linux")
            sudo apt-get update
            sudo apt-get install -y python3.11 python3.11-pip python3.11-venv
            ;;
        "macos")
            if command -v brew &> /dev/null; then
                brew install python@3.11
            else
                print_error "Homebrew not found. Please install Python 3.11 manually."
                exit 1
            fi
            ;;
        "windows")
            print_error "Please install Python 3.11+ manually from python.org"
            exit 1
            ;;
    esac
}

# Install Node.js
install_nodejs() {
    case $OS in
        "linux")
            curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
            sudo apt-get install -y nodejs
            ;;
        "macos")
            if command -v brew &> /dev/null; then
                brew install node@18
            else
                print_error "Homebrew not found. Please install Node.js 18+ manually."
                exit 1
            fi
            ;;
        "windows")
            print_error "Please install Node.js 18+ manually from nodejs.org"
            exit 1
            ;;
    esac
}

# Install uv (Python package manager)
install_uv() {
    print_status "Installing uv (Python package manager)..."
    
    if ! command -v uv &> /dev/null; then
        curl -LsSf https://astral.sh/uv/install.sh | sh
        export PATH="$HOME/.cargo/bin:$PATH"
        print_success "uv installed successfully"
    else
        print_success "uv already installed"
    fi
}

# Install pnpm (Node.js package manager)
install_pnpm() {
    print_status "Installing pnpm (Node.js package manager)..."
    
    if ! command -v pnpm &> /dev/null; then
        npm install -g pnpm
        print_success "pnpm installed successfully"
    else
        print_success "pnpm already installed"
    fi
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        print_success "Backend .env file created"
    else
        print_warning "Backend .env file already exists"
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env.local" ]; then
        cat > frontend/.env.local << EOF
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=Lonors AI Agent Platform
NEXT_PUBLIC_APP_VERSION=0.1.0
EOF
        print_success "Frontend .env.local file created"
    else
        print_warning "Frontend .env.local file already exists"
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing backend dependencies..."
    cd backend
    uv sync --dev
    cd ..
    print_success "Backend dependencies installed"
    
    print_status "Installing frontend dependencies..."
    cd frontend
    pnpm install
    cd ..
    print_success "Frontend dependencies installed"
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    # Start database services
    docker-compose up -d postgres qdrant dragonfly neo4j
    
    # Wait for services to be ready
    print_status "Waiting for database services to be ready..."
    sleep 30
    
    # Run database migrations
    cd backend
    uv run alembic upgrade head
    cd ..
    
    print_success "Database setup completed"
}

# Setup Git hooks
setup_git_hooks() {
    print_status "Setting up Git hooks..."
    
    cd frontend
    if command -v pnpm &> /dev/null; then
        pnpm exec husky install
        print_success "Git hooks installed"
    fi
    cd ..
}

# Create initial admin user
create_admin_user() {
    print_status "Creating initial admin user..."
    
    cd backend
    uv run python -c "
from app.core.database import AsyncSessionLocal
from app.models.user import User
from app.core.security import get_password_hash
import asyncio

async def create_admin():
    async with AsyncSessionLocal() as session:
        admin_user = User(
            email='<EMAIL>',
            username='admin',
            full_name='Administrator',
            hashed_password=get_password_hash('admin123'),
            is_active=True,
            is_superuser=True
        )
        session.add(admin_user)
        await session.commit()
        print('Admin user created: <EMAIL> / admin123')

asyncio.run(create_admin())
"
    cd ..
    
    print_success "Admin user created (<EMAIL> / admin123)"
}

# Verify installation
verify_installation() {
    print_status "Verifying installation..."
    
    # Check if services are running
    if docker-compose ps | grep -q "Up"; then
        print_success "Docker services are running"
    else
        print_error "Some Docker services are not running"
        return 1
    fi
    
    # Test backend health
    if curl -f http://localhost:3000/health &> /dev/null; then
        print_success "Backend is healthy"
    else
        print_warning "Backend health check failed (this is normal if not started yet)"
    fi
    
    # Test frontend build
    cd frontend
    if pnpm build &> /dev/null; then
        print_success "Frontend builds successfully"
    else
        print_error "Frontend build failed"
        return 1
    fi
    cd ..
    
    print_success "Installation verification completed"
}

# Main setup function
main() {
    echo
    print_status "Starting Lonors AI Agent Platform setup..."
    echo
    
    check_os
    check_prerequisites
    install_uv
    install_pnpm
    setup_environment
    install_dependencies
    setup_database
    setup_git_hooks
    create_admin_user
    verify_installation
    
    echo
    print_success "🎉 Setup completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Start the development servers:"
    echo "   ./scripts/dev.sh"
    echo
    echo "2. Open your browser and navigate to:"
    echo "   - Frontend: http://localhost:5500"
    echo "   - Backend API: http://localhost:3000"
    echo "   - API Documentation: http://localhost:3000/docs"
    echo
    echo "3. Login with the admin account:"
    echo "   - Email: <EMAIL>"
    echo "   - Password: admin123"
    echo
    echo "For more information, see the documentation in the docs/ directory."
    echo
}

# Run main function
main "$@"
