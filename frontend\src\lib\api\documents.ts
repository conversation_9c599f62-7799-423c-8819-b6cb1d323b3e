/**
 * API functions for document management
 */

export interface Document {
  id: number
  title: string
  filename: string
  document_type: 'pdf' | 'docx' | 'txt' | 'md'
  status: 'uploaded' | 'processing' | 'processed' | 'failed'
  file_size_mb: number
  chunk_count: number
  created_at: string
  processed_at?: string
  tags?: string[]
  processing_error?: string
}

export interface DocumentStats {
  total_documents: number
  documents_by_type: Record<string, number>
  documents_by_status: Record<string, number>
  total_size_mb: number
  total_chunks: number
  average_chunks_per_document: number
}

export interface DocumentProcessingStatus {
  document_id: number
  status: 'uploaded' | 'processing' | 'processed' | 'failed'
  progress_percentage?: number
  current_step?: string
  error_message?: string
  chunks_processed?: number
  total_chunks?: number
}

export interface SearchQuery {
  query: string
  limit?: number
  threshold?: number
  document_ids?: number[]
  document_types?: string[]
}

export interface SearchResult {
  chunk_id: number
  document_id: number
  document_title: string
  content: string
  similarity_score: number
  chunk_index: number
  page_number?: number
  start_position?: number
  end_position?: number
}

export interface ChatResponse {
  query: string
  response: string
  context: Array<{
    content: string
    source: string
    similarity_score: number
  }>
  sources: string[]
  confidence: number
  context_length: number
}

class DocumentAPI {
  private baseURL = '/api/v1/documents'
  
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('token')
    
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      },
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Request failed' }))
      throw new Error(error.detail || `HTTP ${response.status}`)
    }

    return response.json()
  }

  /**
   * Upload a document
   */
  async uploadDocument(
    file: File, 
    metadata?: { title?: string; tags?: string }
  ): Promise<Document> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (metadata?.title) {
      formData.append('title', metadata.title)
    }
    
    if (metadata?.tags) {
      formData.append('tags', metadata.tags)
    }

    return this.request<Document>('/upload', {
      method: 'POST',
      body: formData,
    })
  }

  /**
   * Get all documents for the current user
   */
  async getDocuments(params?: {
    search?: string
    status?: string
    document_type?: string
  }): Promise<Document[]> {
    const searchParams = new URLSearchParams()
    
    if (params?.search) searchParams.append('search', params.search)
    if (params?.status) searchParams.append('status', params.status)
    if (params?.document_type) searchParams.append('document_type', params.document_type)

    const query = searchParams.toString()
    return this.request<Document[]>(`/${query ? `?${query}` : ''}`)
  }

  /**
   * Get a specific document
   */
  async getDocument(documentId: number): Promise<Document> {
    return this.request<Document>(`/${documentId}`)
  }

  /**
   * Delete a document
   */
  async deleteDocument(documentId: number): Promise<void> {
    await this.request(`/${documentId}`, {
      method: 'DELETE',
    })
  }

  /**
   * Get document processing status
   */
  async getDocumentStatus(documentId: number): Promise<DocumentProcessingStatus> {
    return this.request<DocumentProcessingStatus>(`/${documentId}/status`)
  }

  /**
   * Get document statistics
   */
  async getDocumentStats(): Promise<DocumentStats> {
    return this.request<DocumentStats>('/stats')
  }

  /**
   * Search documents semantically
   */
  async searchDocuments(query: SearchQuery): Promise<SearchResult[]> {
    return this.request<SearchResult[]>('/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(query),
    })
  }

  /**
   * Chat with documents using RAG
   */
  async chatWithDocuments(
    query: string, 
    contextLimit: number = 5
  ): Promise<ChatResponse> {
    const formData = new FormData()
    formData.append('query', query)
    formData.append('context_limit', contextLimit.toString())

    return this.request<ChatResponse>('/chat', {
      method: 'POST',
      body: formData,
    })
  }

  /**
   * Download a document
   */
  async downloadDocument(documentId: number): Promise<Blob> {
    const token = localStorage.getItem('token')
    
    const response = await fetch(`${this.baseURL}/${documentId}/download`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    })

    if (!response.ok) {
      throw new Error('Failed to download document')
    }

    return response.blob()
  }

  /**
   * Get document content for viewing
   */
  async getDocumentContent(documentId: number): Promise<{
    content: string
    content_type: string
  }> {
    return this.request<{
      content: string
      content_type: string
    }>(`/${documentId}/content`)
  }

  /**
   * Reprocess a failed document
   */
  async reprocessDocument(documentId: number): Promise<Document> {
    return this.request<Document>(`/${documentId}/reprocess`, {
      method: 'POST',
    })
  }

  /**
   * Get document chunks
   */
  async getDocumentChunks(documentId: number): Promise<Array<{
    id: number
    chunk_index: number
    content: string
    character_count: number
    token_count: number
    start_position?: number
    end_position?: number
    page_number?: number
    vector_id?: string
  }>> {
    return this.request<Array<{
      id: number
      chunk_index: number
      content: string
      character_count: number
      token_count: number
      start_position?: number
      end_position?: number
      page_number?: number
      vector_id?: string
    }>>(`/${documentId}/chunks`)
  }

  /**
   * Update document metadata
   */
  async updateDocument(
    documentId: number, 
    updates: { title?: string; tags?: string[] }
  ): Promise<Document> {
    return this.request<Document>(`/${documentId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })
  }

  /**
   * Get similar documents
   */
  async getSimilarDocuments(
    documentId: number, 
    limit: number = 5
  ): Promise<Array<{
    document: Document
    similarity_score: number
  }>> {
    return this.request<Array<{
      document: Document
      similarity_score: number
    }>>(`/${documentId}/similar?limit=${limit}`)
  }

  /**
   * Bulk delete documents
   */
  async bulkDeleteDocuments(documentIds: number[]): Promise<void> {
    await this.request('/bulk-delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ document_ids: documentIds }),
    })
  }

  /**
   * Export documents
   */
  async exportDocuments(
    documentIds: number[], 
    format: 'zip' | 'pdf'
  ): Promise<Blob> {
    const token = localStorage.getItem('token')
    
    const response = await fetch(`${this.baseURL}/export`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        document_ids: documentIds,
        format,
      }),
    })

    if (!response.ok) {
      throw new Error('Failed to export documents')
    }

    return response.blob()
  }
}

// Export singleton instance
export const documentAPI = new DocumentAPI()

// Export React Query keys for consistency
export const documentQueryKeys = {
  all: ['documents'] as const,
  lists: () => [...documentQueryKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...documentQueryKeys.lists(), filters] as const,
  details: () => [...documentQueryKeys.all, 'detail'] as const,
  detail: (id: number) => [...documentQueryKeys.details(), id] as const,
  stats: () => [...documentQueryKeys.all, 'stats'] as const,
  status: (id: number) => [...documentQueryKeys.all, 'status', id] as const,
  chunks: (id: number) => [...documentQueryKeys.all, 'chunks', id] as const,
  similar: (id: number) => [...documentQueryKeys.all, 'similar', id] as const,
}
