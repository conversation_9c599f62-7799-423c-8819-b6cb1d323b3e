"""User management endpoints."""

from typing import List, Optional

import structlog
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.dependencies import (
    get_current_admin_user,
    get_current_user,
    require_permissions,
)
from app.core.database import get_db
from app.core.exceptions import NotFoundError, ConflictError
from app.core.security import get_password_hash
from app.models.user import User
from app.schemas.user import (
    User as UserSchema,
    UserCreate,
    UserPublic,
    UserStats,
    UserUpdate,
)

logger = structlog.get_logger(__name__)

router = APIRouter()


@router.get("/", response_model=List[UserPublic])
async def list_users(
    skip: int = Query(0, ge=0, description="Number of users to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of users to return"),
    search: Optional[str] = Query(None, description="Search users by username or email"),
    current_user: User = Depends(require_permissions("read:users")),
    db: AsyncSession = Depends(get_db),
) -> List[UserPublic]:
    """
    List users with pagination and search.
    
    Args:
        skip: Number of users to skip
        limit: Number of users to return
        search: Search term for username or email
        current_user: Current authenticated user
        db: Database session
    
    Returns:
        List[UserPublic]: List of users
    """
    logger.info(
        "Listing users",
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        search=search,
    )
    
    # Build query
    query = select(User)
    
    # Add search filter
    if search:
        search_term = f"%{search}%"
        query = query.where(
            (User.username.ilike(search_term)) |
            (User.email.ilike(search_term)) |
            (User.full_name.ilike(search_term))
        )
    
    # Add pagination
    query = query.offset(skip).limit(limit).order_by(User.created_at.desc())
    
    # Execute query
    result = await db.execute(query)
    users = result.scalars().all()
    
    logger.info("Users listed successfully", count=len(users))
    
    return [UserPublic.from_orm(user) for user in users]


@router.get("/me", response_model=UserSchema)
async def get_current_user_profile(
    current_user: User = Depends(get_current_user),
) -> UserSchema:
    """
    Get current user profile.
    
    Args:
        current_user: Current authenticated user
    
    Returns:
        UserSchema: Current user information
    """
    user_schema = UserSchema.from_orm(current_user)
    user_schema.permissions = current_user.permissions
    return user_schema


@router.put("/me", response_model=UserSchema)
async def update_current_user_profile(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> UserSchema:
    """
    Update current user profile.
    
    Args:
        user_update: User update data
        current_user: Current authenticated user
        db: Database session
    
    Returns:
        UserSchema: Updated user information
    
    Raises:
        HTTPException: If update fails
    """
    logger.info("Updating user profile", user_id=current_user.id)
    
    try:
        # Check for email conflicts
        if user_update.email and user_update.email != current_user.email:
            result = await db.execute(
                select(User).where(
                    (User.email == user_update.email) & (User.id != current_user.id)
                )
            )
            if result.scalar_one_or_none():
                raise ConflictError("Email already in use")
        
        # Check for username conflicts
        if user_update.username and user_update.username != current_user.username:
            result = await db.execute(
                select(User).where(
                    (User.username == user_update.username) & (User.id != current_user.id)
                )
            )
            if result.scalar_one_or_none():
                raise ConflictError("Username already in use")
        
        # Update user fields
        update_data = user_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(current_user, field):
                setattr(current_user, field, value)
        
        await db.commit()
        await db.refresh(current_user)
        
        logger.info("User profile updated successfully", user_id=current_user.id)
        
        user_schema = UserSchema.from_orm(current_user)
        user_schema.permissions = current_user.permissions
        return user_schema
        
    except ConflictError as e:
        logger.warning("User profile update failed", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error("User profile update error", error=str(e), user_id=current_user.id)
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Profile update failed"
        )


@router.get("/me/stats", response_model=UserStats)
async def get_current_user_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> UserStats:
    """
    Get current user statistics.
    
    Args:
        current_user: Current authenticated user
        db: Database session
    
    Returns:
        UserStats: User statistics
    """
    logger.info("Getting user statistics", user_id=current_user.id)
    
    # Count documents
    from app.models.document import Document
    doc_result = await db.execute(
        select(func.count(Document.id)).where(Document.owner_id == current_user.id)
    )
    document_count = doc_result.scalar() or 0
    
    # Calculate total storage (sum of file sizes)
    storage_result = await db.execute(
        select(func.sum(Document.file_size)).where(Document.owner_id == current_user.id)
    )
    total_storage_bytes = storage_result.scalar() or 0
    total_storage_mb = round(total_storage_bytes / (1024 * 1024), 2)
    
    # TODO: Add counts for agents and workflows when those models are implemented
    agent_count = 0
    workflow_count = 0
    
    stats = UserStats(
        document_count=document_count,
        agent_count=agent_count,
        workflow_count=workflow_count,
        total_storage_mb=total_storage_mb,
    )
    
    logger.info("User statistics retrieved", user_id=current_user.id, stats=stats.dict())
    
    return stats


@router.get("/{user_id}", response_model=UserPublic)
async def get_user_by_id(
    user_id: int,
    current_user: User = Depends(require_permissions("read:users")),
    db: AsyncSession = Depends(get_db),
) -> UserPublic:
    """
    Get user by ID.
    
    Args:
        user_id: User ID
        current_user: Current authenticated user
        db: Database session
    
    Returns:
        UserPublic: User information
    
    Raises:
        HTTPException: If user not found
    """
    logger.info("Getting user by ID", user_id=user_id, requester_id=current_user.id)
    
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return UserPublic.from_orm(user)


@router.post("/", response_model=UserSchema, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_create: UserCreate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> UserSchema:
    """
    Create a new user (admin only).
    
    Args:
        user_create: User creation data
        current_user: Current authenticated admin user
        db: Database session
    
    Returns:
        UserSchema: Created user information
    
    Raises:
        HTTPException: If creation fails
    """
    logger.info(
        "Creating new user",
        email=user_create.email,
        username=user_create.username,
        admin_id=current_user.id,
    )
    
    try:
        # Check for email conflicts
        result = await db.execute(select(User).where(User.email == user_create.email))
        if result.scalar_one_or_none():
            raise ConflictError("Email already registered")
        
        # Check for username conflicts
        result = await db.execute(select(User).where(User.username == user_create.username))
        if result.scalar_one_or_none():
            raise ConflictError("Username already taken")
        
        # Create new user
        hashed_password = get_password_hash(user_create.password)
        
        new_user = User(
            email=user_create.email,
            username=user_create.username,
            hashed_password=hashed_password,
            full_name=user_create.full_name,
            bio=user_create.bio,
            avatar_url=user_create.avatar_url,
            role=user_create.role,
            is_active=True,
            is_verified=True,  # Admin-created users are auto-verified
        )
        
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        
        logger.info("User created successfully", user_id=new_user.id, admin_id=current_user.id)
        
        user_schema = UserSchema.from_orm(new_user)
        user_schema.permissions = new_user.permissions
        return user_schema
        
    except ConflictError as e:
        logger.warning("User creation failed", error=str(e), email=user_create.email)
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error("User creation error", error=str(e), email=user_create.email)
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="User creation failed"
        )


@router.put("/{user_id}", response_model=UserSchema)
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> UserSchema:
    """
    Update user (admin only).
    
    Args:
        user_id: User ID to update
        user_update: User update data
        current_user: Current authenticated admin user
        db: Database session
    
    Returns:
        UserSchema: Updated user information
    
    Raises:
        HTTPException: If update fails
    """
    logger.info("Updating user", user_id=user_id, admin_id=current_user.id)
    
    try:
        # Get user to update
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()
        
        if not user:
            raise NotFoundError("User not found")
        
        # Check for email conflicts
        if user_update.email and user_update.email != user.email:
            result = await db.execute(
                select(User).where(
                    (User.email == user_update.email) & (User.id != user_id)
                )
            )
            if result.scalar_one_or_none():
                raise ConflictError("Email already in use")
        
        # Check for username conflicts
        if user_update.username and user_update.username != user.username:
            result = await db.execute(
                select(User).where(
                    (User.username == user_update.username) & (User.id != user_id)
                )
            )
            if result.scalar_one_or_none():
                raise ConflictError("Username already in use")
        
        # Update user fields
        update_data = user_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(user, field):
                setattr(user, field, value)
        
        await db.commit()
        await db.refresh(user)
        
        logger.info("User updated successfully", user_id=user_id, admin_id=current_user.id)
        
        user_schema = UserSchema.from_orm(user)
        user_schema.permissions = user.permissions
        return user_schema
        
    except (NotFoundError, ConflictError) as e:
        logger.warning("User update failed", error=str(e), user_id=user_id)
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error("User update error", error=str(e), user_id=user_id)
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="User update failed"
        )


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
) -> None:
    """
    Delete user (admin only).
    
    Args:
        user_id: User ID to delete
        current_user: Current authenticated admin user
        db: Database session
    
    Raises:
        HTTPException: If deletion fails
    """
    logger.info("Deleting user", user_id=user_id, admin_id=current_user.id)
    
    try:
        # Get user to delete
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()
        
        if not user:
            raise NotFoundError("User not found")
        
        # Prevent self-deletion
        if user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete your own account"
            )
        
        # Delete user (this will cascade to related records)
        await db.delete(user)
        await db.commit()
        
        logger.info("User deleted successfully", user_id=user_id, admin_id=current_user.id)
        
    except NotFoundError as e:
        logger.warning("User deletion failed", error=str(e), user_id=user_id)
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except HTTPException:
        raise
    except Exception as e:
        logger.error("User deletion error", error=str(e), user_id=user_id)
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="User deletion failed"
        )
