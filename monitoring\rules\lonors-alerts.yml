groups:
  - name: lonors.rules
    rules:
      # Service availability alerts
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.job }}"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.job }}"

      # Resource usage alerts
      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total[5m]) * 100 > 80
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% for container {{ $labels.name }}"

      - alert: HighMemoryUsage
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% for container {{ $labels.name }}"

      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk space is {{ $value }}% available on {{ $labels.device }}"

      # Database alerts
      - alert: PostgreSQLDown
        expr: pg_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database has been down for more than 1 minute."

      - alert: PostgreSQLTooManyConnections
        expr: pg_stat_activity_count > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL has too many connections"
          description: "PostgreSQL has {{ $value }} active connections"

      - alert: PostgreSQLSlowQueries
        expr: rate(pg_stat_activity_max_tx_duration[5m]) > 60
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL slow queries detected"
          description: "PostgreSQL has queries running longer than 60 seconds"

      # Redis alerts
      - alert: RedisDown
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis cache has been down for more than 1 minute."

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is {{ $value }}%"

      # Qdrant alerts
      - alert: QdrantDown
        expr: up{job="qdrant"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Qdrant vector database is down"
          description: "Qdrant has been down for more than 1 minute."

      # Application-specific alerts
      - alert: DocumentProcessingBacklog
        expr: lonors_documents_processing > 100
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Document processing backlog"
          description: "{{ $value }} documents are currently being processed"

      - alert: HighSearchLatency
        expr: histogram_quantile(0.95, rate(lonors_search_duration_seconds_bucket[5m])) > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High search latency"
          description: "95th percentile search latency is {{ $value }}s"

      - alert: HighChatLatency
        expr: histogram_quantile(0.95, rate(lonors_chat_duration_seconds_bucket[5m])) > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High chat response latency"
          description: "95th percentile chat latency is {{ $value }}s"

      - alert: EmbeddingServiceError
        expr: rate(lonors_embedding_errors_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Embedding service errors"
          description: "Embedding service error rate is {{ $value }} errors/sec"

      - alert: VectorSearchError
        expr: rate(lonors_vector_search_errors_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Vector search errors"
          description: "Vector search error rate is {{ $value }} errors/sec"

      # Security alerts
      - alert: HighFailedLoginRate
        expr: rate(lonors_auth_failures_total[5m]) > 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High failed login rate"
          description: "Failed login rate is {{ $value }} attempts/sec"

      - alert: SuspiciousActivity
        expr: rate(http_requests_total{status="403"}[5m]) > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Suspicious activity detected"
          description: "High rate of 403 responses: {{ $value }} requests/sec"

      # File system alerts
      - alert: UploadDirectoryFull
        expr: (node_filesystem_avail_bytes{mountpoint="/app/uploads"} / node_filesystem_size_bytes{mountpoint="/app/uploads"}) * 100 < 5
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Upload directory almost full"
          description: "Upload directory has only {{ $value }}% space remaining"
