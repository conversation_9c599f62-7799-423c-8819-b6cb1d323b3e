import http from 'k6/http';
import { check, sleep } from 'k6';
import { FormData } from 'https://jslib.k6.io/formdata/0.0.2/index.js';

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up to 10 users
    { duration: '5m', target: 10 }, // Stay at 10 users
    { duration: '2m', target: 20 }, // Ramp up to 20 users
    { duration: '5m', target: 20 }, // Stay at 20 users
    { duration: '2m', target: 0 },  // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<5000'], // 95% of requests must complete below 5s
    http_req_failed: ['rate<0.1'],     // Error rate must be below 10%
  },
};

// Test data
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const AUTH_TOKEN = __ENV.AUTH_TOKEN || 'test-token';

// Sample document content
const sampleDocuments = [
  {
    name: 'test-document-1.txt',
    content: 'This is a test document for load testing. '.repeat(100),
    type: 'text/plain'
  },
  {
    name: 'test-document-2.txt',
    content: 'Another test document with different content. '.repeat(150),
    type: 'text/plain'
  },
  {
    name: 'test-document-3.txt',
    content: 'Third test document for comprehensive testing. '.repeat(200),
    type: 'text/plain'
  }
];

export function setup() {
  // Authenticate and get token if needed
  const loginResponse = http.post(`${BASE_URL}/api/v1/auth/login`, {
    email: '<EMAIL>',
    password: 'testpassword'
  });
  
  if (loginResponse.status === 200) {
    const token = loginResponse.json('access_token');
    return { token };
  }
  
  return { token: AUTH_TOKEN };
}

export default function(data) {
  const token = data.token;
  const headers = {
    'Authorization': `Bearer ${token}`,
  };

  // Test document upload
  testDocumentUpload(headers);
  
  // Test document listing
  testDocumentListing(headers);
  
  // Test document search
  testDocumentSearch(headers);
  
  // Test document chat
  testDocumentChat(headers);
  
  sleep(1);
}

function testDocumentUpload(headers) {
  const document = sampleDocuments[Math.floor(Math.random() * sampleDocuments.length)];
  
  const formData = new FormData();
  formData.append('file', http.file(document.content, document.name, document.type));
  formData.append('title', `Load Test Document ${Date.now()}`);
  formData.append('tags', 'load-test,performance');

  const response = http.post(`${BASE_URL}/api/v1/documents/upload`, formData.body(), {
    headers: {
      ...headers,
      'Content-Type': 'multipart/form-data; boundary=' + formData.boundary,
    },
  });

  check(response, {
    'upload status is 201': (r) => r.status === 201,
    'upload response has id': (r) => r.json('id') !== undefined,
    'upload response time < 10s': (r) => r.timings.duration < 10000,
  });
}

function testDocumentListing(headers) {
  const response = http.get(`${BASE_URL}/api/v1/documents/`, { headers });

  check(response, {
    'list status is 200': (r) => r.status === 200,
    'list response is array': (r) => Array.isArray(r.json()),
    'list response time < 2s': (r) => r.timings.duration < 2000,
  });
}

function testDocumentSearch(headers) {
  const searchQueries = [
    'test document',
    'load testing',
    'performance',
    'content analysis',
    'document processing'
  ];
  
  const query = searchQueries[Math.floor(Math.random() * searchQueries.length)];
  
  const payload = {
    query: query,
    limit: 10,
    threshold: 0.7
  };

  const response = http.post(`${BASE_URL}/api/v1/documents/search`, JSON.stringify(payload), {
    headers: {
      ...headers,
      'Content-Type': 'application/json',
    },
  });

  check(response, {
    'search status is 200': (r) => r.status === 200,
    'search response is array': (r) => Array.isArray(r.json()),
    'search response time < 3s': (r) => r.timings.duration < 3000,
  });
}

function testDocumentChat(headers) {
  const chatQueries = [
    'What is this document about?',
    'Summarize the main points',
    'What are the key findings?',
    'Explain the methodology',
    'What are the conclusions?'
  ];
  
  const query = chatQueries[Math.floor(Math.random() * chatQueries.length)];
  
  const formData = new FormData();
  formData.append('query', query);
  formData.append('context_limit', '5');

  const response = http.post(`${BASE_URL}/api/v1/documents/chat`, formData.body(), {
    headers: {
      ...headers,
      'Content-Type': 'multipart/form-data; boundary=' + formData.boundary,
    },
  });

  check(response, {
    'chat status is 200': (r) => r.status === 200,
    'chat response has query': (r) => r.json('query') !== undefined,
    'chat response has response': (r) => r.json('response') !== undefined,
    'chat response time < 5s': (r) => r.timings.duration < 5000,
  });
}

export function teardown(data) {
  // Cleanup test data if needed
  console.log('Load test completed');
}
