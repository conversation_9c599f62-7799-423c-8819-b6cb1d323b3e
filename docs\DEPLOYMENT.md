# Lonors RAG Platform - Production Deployment Guide

## 🚀 Overview

This guide provides comprehensive instructions for deploying the Lonors RAG Platform to production environments. The deployment includes automated CI/CD pipelines, monitoring, logging, and health checks.

## 📋 Prerequisites

### System Requirements

**Minimum Hardware:**
- CPU: 4 cores (8 recommended)
- RAM: 8GB (16GB recommended)
- Storage: 100GB SSD (500GB recommended)
- Network: 1Gbps connection

**Software Requirements:**
- Docker 24.0+
- Docker Compose 2.20+
- Git 2.30+
- curl, wget, nc (netcat)

### Environment Setup

1. **Clone Repository**
   ```bash
   git clone https://github.com/your-org/lonors-rag-platform.git
   cd lonors-rag-platform
   ```

2. **Configure Environment Variables**
   ```bash
   # Copy and customize environment files
   cp .env.staging .env.local
   cp .env.production .env.prod
   
   # Edit with your specific values
   nano .env.prod
   ```

3. **Generate Secrets**
   ```bash
   # Generate secure passwords and keys
   openssl rand -base64 32  # For SECRET_KEY
   openssl rand -base64 32  # For JWT_SECRET_KEY
   openssl rand -base64 16  # For database passwords
   ```

## 🔧 Deployment Methods

### Method 1: Automated Deployment (Recommended)

```bash
# Make deployment script executable
chmod +x scripts/deploy.sh

# Deploy to staging
./scripts/deploy.sh staging latest

# Deploy to production
./scripts/deploy.sh production v1.0.0
```

### Method 2: Manual Deployment

```bash
# 1. Build images
docker-compose -f docker-compose.production.yml build

# 2. Start services
docker-compose -f docker-compose.production.yml up -d

# 3. Run migrations
docker-compose -f docker-compose.production.yml exec backend uv run alembic upgrade head

# 4. Verify deployment
python scripts/health-check.py
```

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Nginx       │    │    Frontend     │    │    Backend      │
│  Load Balancer  │────│   (Next.js)     │────│   (FastAPI)     │
│   Port 80/443   │    │   Port 5500     │    │   Port 3000     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐    ┌─────────────────┐
         │              │   PostgreSQL    │    │     Redis       │
         │              │   Port 5432     │    │   Port 6379     │
         │              └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐    ┌─────────────────┐
         │              │     Qdrant      │    │   Monitoring    │
         │              │   Port 6333     │    │  (Prometheus)   │
         │              └─────────────────┘    └─────────────────┘
         │
┌─────────────────┐
│    Grafana      │
│   Port 3001     │
└─────────────────┘
```

## 🔐 Security Configuration

### SSL/TLS Setup

1. **Obtain SSL Certificates**
   ```bash
   # Using Let's Encrypt (recommended)
   certbot certonly --standalone -d lonors.com -d www.lonors.com
   
   # Copy certificates to nginx directory
   cp /etc/letsencrypt/live/lonors.com/fullchain.pem nginx/ssl/cert.pem
   cp /etc/letsencrypt/live/lonors.com/privkey.pem nginx/ssl/key.pem
   ```

2. **Configure Firewall**
   ```bash
   # Allow necessary ports
   ufw allow 22    # SSH
   ufw allow 80    # HTTP
   ufw allow 443   # HTTPS
   ufw enable
   ```

### Environment Security

- **Never commit secrets to version control**
- **Use strong, unique passwords for all services**
- **Enable 2FA for all admin accounts**
- **Regularly rotate API keys and passwords**
- **Monitor access logs for suspicious activity**

## 📊 Monitoring & Observability

### Metrics Collection

**Prometheus Metrics Available:**
- HTTP request rates and latencies
- Database connection pools
- Redis cache hit rates
- Document processing metrics
- Search and chat performance
- System resource usage

**Access Monitoring:**
- Prometheus: `http://localhost:9090`
- Grafana: `http://localhost:3001`
- Default Grafana credentials: `admin` / `${GRAFANA_PASSWORD}`

### Log Aggregation

**Loki + Promtail Setup:**
- Centralized log collection from all containers
- Structured logging with JSON format
- Log retention and rotation policies
- Real-time log streaming and search

### Alerting Rules

**Critical Alerts:**
- Service downtime (>1 minute)
- High error rates (>10%)
- Resource exhaustion (CPU >80%, Memory >85%)
- Database connection failures
- Disk space low (<10%)

**Warning Alerts:**
- High response times (>2 seconds)
- Processing backlogs
- Cache miss rates
- SSL certificate expiration

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow

**Triggers:**
- Push to `main` branch → Production deployment
- Push to `develop` branch → Staging deployment
- Pull requests → Testing and validation

**Pipeline Stages:**
1. **Code Quality**: Linting, type checking, security scans
2. **Testing**: Unit tests, integration tests, coverage reports
3. **Building**: Docker image creation and optimization
4. **Security**: Vulnerability scanning with Trivy
5. **Deployment**: Automated deployment to target environment
6. **Verification**: Health checks and smoke tests

### Deployment Strategies

**Blue-Green Deployment:**
```bash
# Deploy new version alongside current
docker-compose -f docker-compose.blue.yml up -d

# Switch traffic after validation
./scripts/switch-traffic.sh blue

# Remove old version
docker-compose -f docker-compose.green.yml down
```

**Rolling Updates:**
```bash
# Update services one by one
docker-compose -f docker-compose.production.yml up -d --no-deps backend
docker-compose -f docker-compose.production.yml up -d --no-deps frontend
```

## 🧪 Testing & Validation

### Health Checks

```bash
# Comprehensive health check
python scripts/health-check.py

# Individual service checks
curl -f http://localhost:3000/health
curl -f http://localhost:5500
```

### Load Testing

```bash
# Install k6
curl https://github.com/grafana/k6/releases/download/v0.47.0/k6-v0.47.0-linux-amd64.tar.gz -L | tar xvz --strip-components 1

# Run load tests
k6 run tests/load/document-upload-test.js
```

### Performance Benchmarks

**Target Metrics:**
- Document upload: <10s for 50MB files
- Search response: <2s for 95th percentile
- Chat response: <5s for 95th percentile
- API availability: >99.9%
- Error rate: <0.1%

## 🔧 Maintenance & Operations

### Backup Procedures

**Database Backup:**
```bash
# Automated daily backups
docker-compose exec postgres pg_dump -U lonors_prod lonors_production > backup_$(date +%Y%m%d).sql

# Restore from backup
docker-compose exec -T postgres psql -U lonors_prod lonors_production < backup_20240101.sql
```

**File Storage Backup:**
```bash
# Backup uploaded documents
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz /app/uploads/

# Sync to remote storage
aws s3 sync /app/uploads/ s3://lonors-backups/uploads/
```

### Log Rotation

```bash
# Configure logrotate
cat > /etc/logrotate.d/lonors << EOF
/var/log/lonors/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 lonors lonors
}
EOF
```

### Update Procedures

1. **Prepare Update**
   ```bash
   # Create backup
   ./scripts/backup.sh
   
   # Test in staging
   ./scripts/deploy.sh staging v1.1.0
   ```

2. **Deploy Update**
   ```bash
   # Deploy to production
   ./scripts/deploy.sh production v1.1.0
   
   # Verify deployment
   python scripts/health-check.py
   ```

3. **Rollback (if needed)**
   ```bash
   # Rollback to previous version
   ./scripts/rollback.sh v1.0.0
   ```

## 🚨 Troubleshooting

### Common Issues

**Service Won't Start:**
```bash
# Check logs
docker-compose logs backend
docker-compose logs frontend

# Check resource usage
docker stats

# Restart services
docker-compose restart backend
```

**Database Connection Issues:**
```bash
# Check PostgreSQL status
docker-compose exec postgres pg_isready

# Check connection pool
docker-compose exec backend python -c "from app.core.database import engine; print(engine.pool.status())"
```

**High Memory Usage:**
```bash
# Check memory usage by container
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# Restart memory-intensive services
docker-compose restart backend
```

### Emergency Procedures

**Complete System Failure:**
1. Check system resources and disk space
2. Restart all services: `docker-compose restart`
3. If persistent, restore from backup
4. Contact support team

**Data Corruption:**
1. Stop affected services immediately
2. Restore database from latest backup
3. Verify data integrity
4. Resume services gradually

## 📞 Support & Contacts

**Emergency Contacts:**
- DevOps Team: <EMAIL>
- Platform Team: <EMAIL>
- Security Team: <EMAIL>

**Monitoring Alerts:**
- Slack: #lonors-alerts
- PagerDuty: lonors-production
- Email: <EMAIL>

**Documentation:**
- Internal Wiki: https://wiki.lonors.com
- API Docs: https://api.lonors.com/docs
- Status Page: https://status.lonors.com

---

## ✅ Deployment Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] SSL certificates installed
- [ ] Firewall rules configured
- [ ] Backup procedures tested
- [ ] Monitoring dashboards configured

### Deployment
- [ ] Code deployed successfully
- [ ] Database migrations completed
- [ ] All services started
- [ ] Health checks passing
- [ ] Load balancer configured

### Post-Deployment
- [ ] Performance metrics validated
- [ ] Error rates within acceptable limits
- [ ] Monitoring alerts configured
- [ ] Documentation updated
- [ ] Team notified of deployment

### Production Readiness
- [ ] SSL/TLS properly configured
- [ ] Security headers enabled
- [ ] Rate limiting configured
- [ ] Backup automation working
- [ ] Disaster recovery plan tested
