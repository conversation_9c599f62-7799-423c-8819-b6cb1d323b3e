"""Pytest configuration and fixtures."""

import asyncio
from typing import Async<PERSON>enerator, Generator

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import St<PERSON><PERSON>ool

from app.core.config import get_settings
from app.core.database import Base, get_db
from app.core.security import create_access_token, get_password_hash
from app.main import app
from app.models.user import User, UserRoleEnum

# Test database URL (in-memory SQLite)
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# Create test engine
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
    echo=False,
)

# Create test session factory
TestSessionLocal = sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False,
)


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session."""
    # Create tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    async with TestSessionLocal() as session:
        yield session
    
    # Drop tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture
def client(db_session: AsyncSession) -> TestClient:
    """Create a test client with database dependency override."""
    
    async def override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def async_client(db_session: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """Create an async test client."""
    
    async def override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = override_get_db
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def test_user(db_session: AsyncSession) -> User:
    """Create a test user."""
    user = User(
        email="<EMAIL>",
        username="testuser",
        hashed_password=get_password_hash("testpassword123"),
        full_name="Test User",
        is_active=True,
        is_verified=True,
        role=UserRoleEnum.USER,
    )
    
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    return user


@pytest_asyncio.fixture
async def admin_user(db_session: AsyncSession) -> User:
    """Create an admin test user."""
    user = User(
        email="<EMAIL>",
        username="adminuser",
        hashed_password=get_password_hash("adminpassword123"),
        full_name="Admin User",
        is_active=True,
        is_verified=True,
        is_superuser=True,
        role=UserRoleEnum.ADMIN,
    )
    
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    return user


@pytest.fixture
def user_token(test_user: User) -> str:
    """Create a JWT token for test user."""
    return create_access_token(
        subject=test_user.id,
        additional_claims={
            "username": test_user.username,
            "role": test_user.role.value,
            "permissions": test_user.permissions,
        }
    )


@pytest.fixture
def admin_token(admin_user: User) -> str:
    """Create a JWT token for admin user."""
    return create_access_token(
        subject=admin_user.id,
        additional_claims={
            "username": admin_user.username,
            "role": admin_user.role.value,
            "permissions": admin_user.permissions,
        }
    )


@pytest.fixture
def auth_headers(user_token: str) -> dict[str, str]:
    """Create authorization headers for test user."""
    return {"Authorization": f"Bearer {user_token}"}


@pytest.fixture
def admin_headers(admin_token: str) -> dict[str, str]:
    """Create authorization headers for admin user."""
    return {"Authorization": f"Bearer {admin_token}"}


@pytest.fixture
def settings():
    """Get test settings."""
    return get_settings()


# Test data factories
class UserFactory:
    """Factory for creating test users."""
    
    @staticmethod
    def build(**kwargs) -> dict:
        """Build user data."""
        default_data = {
            "email": "<EMAIL>",
            "username": "testuser",
            "password": "TestPassword123!",
            "full_name": "Test User",
        }
        default_data.update(kwargs)
        return default_data
    
    @staticmethod
    async def create(db_session: AsyncSession, **kwargs) -> User:
        """Create a user in the database."""
        user_data = UserFactory.build(**kwargs)
        
        user = User(
            email=user_data["email"],
            username=user_data["username"],
            hashed_password=get_password_hash(user_data["password"]),
            full_name=user_data.get("full_name"),
            is_active=user_data.get("is_active", True),
            is_verified=user_data.get("is_verified", True),
            role=user_data.get("role", UserRoleEnum.USER),
        )
        
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        
        return user


@pytest.fixture
def user_factory():
    """User factory fixture."""
    return UserFactory
