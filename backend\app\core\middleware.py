"""Custom middleware for the FastAPI application."""

import time
from typing import Callable

import structlog
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from app.core.config import get_settings
from app.core.logging import (
    get_correlation_id,
    log_api_request,
    log_security_event,
    set_correlation_id,
)

settings = get_settings()
logger = structlog.get_logger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging HTTP requests and responses."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and log details."""
        # Generate or extract correlation ID
        correlation_id = request.headers.get("X-Correlation-ID") or get_correlation_id()
        set_correlation_id(correlation_id)
        
        # Record start time
        start_time = time.time()
        
        # Extract request details
        method = request.method
        path = request.url.path
        query_params = dict(request.query_params)
        user_agent = request.headers.get("User-Agent", "")
        client_ip = self._get_client_ip(request)
        
        # Log request start
        logger.info(
            "Request started",
            method=method,
            path=path,
            query_params=query_params,
            user_agent=user_agent,
            client_ip=client_ip,
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration_ms = (time.time() - start_time) * 1000
            
            # Add correlation ID to response headers
            response.headers["X-Correlation-ID"] = correlation_id
            
            # Log request completion
            log_api_request(
                method=method,
                path=path,
                status_code=response.status_code,
                duration_ms=duration_ms,
                client_ip=client_ip,
                user_agent=user_agent,
            )
            
            return response
            
        except Exception as e:
            # Calculate duration for failed requests
            duration_ms = (time.time() - start_time) * 1000
            
            # Log error
            logger.error(
                "Request failed",
                method=method,
                path=path,
                duration_ms=duration_ms,
                error=str(e),
                client_ip=client_ip,
            )
            
            # Return error response
            return JSONResponse(
                status_code=500,
                content={"detail": "Internal server error", "correlation_id": correlation_id},
                headers={"X-Correlation-ID": correlation_id},
            )
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        return request.client.host if request.client else "unknown"


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware for adding security headers to responses."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add security headers to response."""
        response = await call_next(request)
        
        # Add security headers
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' data:; "
                "connect-src 'self' https:; "
                "frame-ancestors 'none';"
            ),
            "Permissions-Policy": (
                "camera=(), microphone=(), geolocation=(), "
                "payment=(), usb=(), magnetometer=(), gyroscope=()"
            ),
        }
        
        # Add HSTS header for HTTPS
        if request.url.scheme == "https":
            security_headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        # Apply headers
        for header, value in security_headers.items():
            response.headers[header] = value
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware for rate limiting requests."""
    
    def __init__(self, app, redis_client=None):
        super().__init__(app)
        self.redis_client = redis_client
        self.requests_per_minute = settings.RATE_LIMIT_REQUESTS_PER_MINUTE
        self.burst_limit = settings.RATE_LIMIT_BURST
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Apply rate limiting to requests."""
        if not self.redis_client:
            # Skip rate limiting if Redis is not available
            return await call_next(request)
        
        # Get client identifier
        client_id = self._get_client_identifier(request)
        
        # Check rate limit
        if await self._is_rate_limited(client_id, request):
            log_security_event(
                event_type="rate_limit_exceeded",
                severity="medium",
                client_id=client_id,
                path=request.url.path,
                method=request.method,
            )
            
            return JSONResponse(
                status_code=429,
                content={
                    "detail": "Rate limit exceeded",
                    "error_code": "RATE_LIMIT_ERROR",
                },
                headers={
                    "Retry-After": "60",
                    "X-RateLimit-Limit": str(self.requests_per_minute),
                    "X-RateLimit-Remaining": "0",
                },
            )
        
        return await call_next(request)
    
    def _get_client_identifier(self, request: Request) -> str:
        """Get unique identifier for the client."""
        # Try to get user ID from authentication
        user_id = getattr(request.state, "user_id", None)
        if user_id:
            return f"user:{user_id}"
        
        # Fall back to IP address
        client_ip = request.headers.get("X-Forwarded-For", "").split(",")[0].strip()
        if not client_ip:
            client_ip = request.headers.get("X-Real-IP", "")
        if not client_ip and request.client:
            client_ip = request.client.host
        
        return f"ip:{client_ip or 'unknown'}"
    
    async def _is_rate_limited(self, client_id: str, request: Request) -> bool:
        """Check if client has exceeded rate limit."""
        try:
            # Use sliding window rate limiting
            now = int(time.time())
            window_start = now - 60  # 1 minute window
            
            # Redis key for this client
            key = f"rate_limit:{client_id}"
            
            # Use Redis pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            
            # Remove old entries
            pipe.zremrangebyscore(key, 0, window_start)
            
            # Count current requests
            pipe.zcard(key)
            
            # Add current request
            pipe.zadd(key, {str(now): now})
            
            # Set expiration
            pipe.expire(key, 120)  # 2 minutes to be safe
            
            # Execute pipeline
            results = await pipe.execute()
            current_requests = results[1]
            
            # Check if limit exceeded
            return current_requests >= self.requests_per_minute
            
        except Exception as e:
            logger.error("Rate limiting error", error=str(e))
            # Allow request if rate limiting fails
            return False


class MonitoringMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting application metrics."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Collect metrics for monitoring."""
        start_time = time.time()
        
        try:
            response = await call_next(request)
            duration = time.time() - start_time
            
            # Record metrics (would integrate with Prometheus here)
            self._record_request_metric(
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                duration=duration,
            )
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            
            # Record error metrics
            self._record_request_metric(
                method=request.method,
                path=request.url.path,
                status_code=500,
                duration=duration,
                error=True,
            )
            
            raise
    
    def _record_request_metric(
        self,
        method: str,
        path: str,
        status_code: int,
        duration: float,
        error: bool = False,
    ) -> None:
        """Record request metrics."""
        # This would integrate with Prometheus metrics
        # For now, just log the metrics
        logger.info(
            "Request metric",
            metric_type="http_request",
            method=method,
            path=path,
            status_code=status_code,
            duration_seconds=duration,
            error=error,
        )
