[project]
name = "lonors-backend"
version = "0.1.0"
description = "Lonors AI Agent Platform Backend"
authors = [
    {name = "Lonors Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"
keywords = ["ai", "agents", "rag", "langchain", "fastapi"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    # Web Framework
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    
    # Database
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    "asyncpg>=0.29.0",
    "psycopg2-binary>=2.9.0",
    
    # Vector Database
    "qdrant-client>=1.7.0",
    
    # Cache
    "redis>=5.0.0",
    
    # Graph Database
    "neo4j>=5.15.0",
    
    # AI/ML
    "langchain>=0.1.0",
    "langchain-community>=0.0.10",
    "langgraph>=0.0.20",
    "openai>=1.6.0",
    "anthropic>=0.8.0",
    "transformers>=4.36.0",
    "torch>=2.1.0",
    "sentence-transformers>=2.2.0",
    "qdrant-client>=1.7.0",
    "numpy>=1.24.0",
    "whisper>=1.1.10",
    
    # Document Processing
    "pypdf>=3.17.0",
    "python-docx>=1.1.0",
    "python-multipart>=0.0.6",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "markdown>=3.5.0",
    "python-magic>=0.4.27",
    "aiofiles>=23.2.0",
    "pillow>=10.1.0",

    # Multi-modal Processing
    "pytesseract>=0.3.10",
    "opencv-python>=4.8.0",
    "pdf2image>=1.16.3",
    "easyocr>=1.7.0",
    "librosa>=0.10.1",
    "soundfile>=0.12.1",
    "pydub>=0.25.1",

    # External LLM Integration
    "openai>=1.3.0",
    "anthropic>=0.7.0",
    "tiktoken>=0.5.0",
    
    # Web Scraping
    "scrapy>=2.11.0",
    "selenium>=4.16.0",
    "requests>=2.31.0",
    "httpx>=0.25.0",
    
    # Authentication & Security
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    "cryptography>=41.0.0",
    
    # Utilities
    "python-dotenv>=1.0.0",
    "structlog>=23.2.0",
    "rich>=13.7.0",
    "typer>=0.9.0",
    "celery>=5.3.0",
    "flower>=2.0.0",
    
    # Monitoring
    "prometheus-client>=0.19.0",
    "sentry-sdk[fastapi]>=1.38.0",
    
    # File Storage
    "boto3>=1.34.0",
    "minio>=7.2.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.0",
    "factory-boy>=3.3.0",
    
    # Code Quality
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.0",
    "bandit>=1.7.0",
    "safety>=2.3.0",
    
    # Documentation
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocstrings[python]>=0.24.0",
    
    # Development Tools
    "pre-commit>=3.6.0",
    "ipython>=8.17.0",
    "jupyter>=1.0.0",
]

[project.urls]
Homepage = "https://github.com/lonors/lonors"
Documentation = "https://docs.lonors.ai"
Repository = "https://github.com/lonors/lonors"
Issues = "https://github.com/lonors/lonors/issues"

[project.scripts]
lonors = "app.cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.version]
path = "app/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["app"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

# mypy configuration
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "qdrant_client.*",
    "neo4j.*",
    "langchain.*",
    "langgraph.*",
    "transformers.*",
    "torch.*",
    "whisper.*",
    "scrapy.*",
    "selenium.*",
]
ignore_missing_imports = true

# pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Bandit configuration
[tool.bandit]
exclude_dirs = ["tests", "migrations"]
skips = ["B101", "B601"]
