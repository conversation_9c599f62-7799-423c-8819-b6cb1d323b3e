"""API endpoints for advanced AI features."""

from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.document import Document
from app.services.ai_features import ai_features_service
from app.services.llm_service import llm_service
from app.services.rag_service import rag_service
import structlog

logger = structlog.get_logger(__name__)

router = APIRouter(prefix="/ai", tags=["AI Features"])


# Request/Response Models
class SummarizeRequest(BaseModel):
    """Request model for document summarization."""
    document_id: int = Field(..., description="Document ID to summarize")
    style: str = Field(default="concise", description="Summary style (concise, detailed, bullet-points)")
    max_length: int = Field(default=500, description="Maximum summary length")
    use_llm: bool = Field(default=True, description="Use external LLM for summarization")


class SummarizeResponse(BaseModel):
    """Response model for document summarization."""
    document_id: int
    summary: str
    style: str
    original_length: int
    summary_length: int
    compression_ratio: float
    model_used: Optional[str] = None
    method: str


class CategorizeRequest(BaseModel):
    """Request model for document categorization."""
    document_id: int = Field(..., description="Document ID to categorize")
    categories: Optional[List[str]] = Field(default=None, description="Predefined categories")


class CategorizeResponse(BaseModel):
    """Response model for document categorization."""
    document_id: int
    category: str
    confidence: float
    method: str
    all_scores: Optional[Dict[str, float]] = None


class AnalyzeRequest(BaseModel):
    """Request model for comprehensive document analysis."""
    document_id: int = Field(..., description="Document ID to analyze")


class AnalyzeResponse(BaseModel):
    """Response model for comprehensive document analysis."""
    document_id: int
    text_length: int
    summary: Dict
    categorization: Dict
    sentiment: Dict
    entities: Dict
    analysis_timestamp: float


class ChatRequest(BaseModel):
    """Request model for enhanced chat."""
    query: str = Field(..., description="User query")
    model: str = Field(default="auto", description="LLM model to use")
    max_tokens: int = Field(default=1000, description="Maximum response tokens")
    temperature: float = Field(default=0.7, description="Response creativity")
    context_limit: int = Field(default=5, description="Maximum context chunks")


class ChatResponse(BaseModel):
    """Response model for enhanced chat."""
    query: str
    response: str
    context: List[Dict]
    sources: List[str]
    confidence: float
    context_length: int
    response_metadata: Dict


# API Endpoints
@router.post("/summarize", response_model=SummarizeResponse)
async def summarize_document(
    request: SummarizeRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Generate document summary using AI.
    
    Supports both extractive and LLM-based summarization methods.
    """
    logger.info(
        "Document summarization requested",
        document_id=request.document_id,
        user_id=current_user.id,
        style=request.style
    )
    
    try:
        # Get document and verify ownership
        document = await _get_user_document(db, request.document_id, current_user.id)
        
        # Get document text
        document_text = await _get_document_text(document)
        
        # Generate summary
        result = await rag_service.generate_document_summary(
            document_id=request.document_id,
            text=document_text,
            style=request.style,
            use_llm=request.use_llm
        )
        
        logger.info(
            "Document summarization completed",
            document_id=request.document_id,
            compression_ratio=result.get("compression_ratio", 0)
        )
        
        return SummarizeResponse(**result)
        
    except Exception as e:
        logger.error(
            "Document summarization failed",
            document_id=request.document_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Summarization failed: {str(e)}"
        )


@router.post("/categorize", response_model=CategorizeResponse)
async def categorize_document(
    request: CategorizeRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Automatically categorize document based on content.
    
    Supports both supervised (with predefined categories) and unsupervised categorization.
    """
    logger.info(
        "Document categorization requested",
        document_id=request.document_id,
        user_id=current_user.id,
        categories=request.categories
    )
    
    try:
        # Get document and verify ownership
        document = await _get_user_document(db, request.document_id, current_user.id)
        
        # Get document text
        document_text = await _get_document_text(document)
        
        # Categorize document
        result = await ai_features_service.categorize_document(
            text=document_text,
            categories=request.categories
        )
        
        result["document_id"] = request.document_id
        
        logger.info(
            "Document categorization completed",
            document_id=request.document_id,
            category=result["category"],
            confidence=result["confidence"]
        )
        
        return CategorizeResponse(**result)
        
    except Exception as e:
        logger.error(
            "Document categorization failed",
            document_id=request.document_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Categorization failed: {str(e)}"
        )


@router.post("/analyze", response_model=AnalyzeResponse)
async def analyze_document(
    request: AnalyzeRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Perform comprehensive document analysis.
    
    Includes summarization, categorization, sentiment analysis, and entity extraction.
    """
    logger.info(
        "Document analysis requested",
        document_id=request.document_id,
        user_id=current_user.id
    )
    
    try:
        # Get document and verify ownership
        document = await _get_user_document(db, request.document_id, current_user.id)
        
        # Get document text
        document_text = await _get_document_text(document)
        
        # Perform comprehensive analysis
        result = await rag_service.analyze_document(
            document_id=request.document_id,
            text=document_text
        )
        
        logger.info(
            "Document analysis completed",
            document_id=request.document_id,
            text_length=result["text_length"]
        )
        
        return AnalyzeResponse(**result)
        
    except Exception as e:
        logger.error(
            "Document analysis failed",
            document_id=request.document_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Analysis failed: {str(e)}"
        )


@router.post("/chat/enhanced", response_model=ChatResponse)
async def enhanced_chat(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Generate enhanced chat response using external LLMs.
    
    Combines RAG context with advanced language models for high-quality responses.
    """
    logger.info(
        "Enhanced chat requested",
        user_id=current_user.id,
        query_length=len(request.query),
        model=request.model
    )
    
    try:
        # Generate RAG response with LLM enhancement
        result = await rag_service.generate_rag_response(
            query=request.query,
            context_limit=request.context_limit,
            user_id=current_user.id
        )
        
        logger.info(
            "Enhanced chat completed",
            user_id=current_user.id,
            response_length=len(result["response"]),
            context_chunks=len(result["context"])
        )
        
        return ChatResponse(**result)
        
    except Exception as e:
        logger.error(
            "Enhanced chat failed",
            user_id=current_user.id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chat failed: {str(e)}"
        )


@router.get("/models")
async def get_available_models(
    current_user: User = Depends(get_current_user)
):
    """
    Get list of available AI models.
    
    Returns available models from configured LLM providers.
    """
    try:
        models = await llm_service.get_available_models()
        
        return {
            "models": models,
            "default_model": "auto",
            "supported_providers": list(models.keys())
        }
        
    except Exception as e:
        logger.error("Failed to get available models", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get models: {str(e)}"
        )


@router.get("/health")
async def ai_features_health():
    """
    Check health of AI features services.
    
    Returns status of all AI components including LLM services.
    """
    try:
        # Check AI features service
        ai_health = await ai_features_service.health_check()
        
        # Check LLM service
        llm_health = await llm_service.health_check()
        
        # Overall status
        overall_status = "healthy"
        if ai_health["status"] != "healthy" or llm_health["status"] not in ["healthy", "not_configured"]:
            overall_status = "unhealthy"
        
        return {
            "status": overall_status,
            "ai_features": ai_health,
            "llm_service": llm_health,
            "timestamp": "2024-01-01T00:00:00Z"  # Would use actual timestamp
        }
        
    except Exception as e:
        logger.error("AI features health check failed", error=str(e))
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": "2024-01-01T00:00:00Z"
        }


# Helper Functions
async def _get_user_document(db: AsyncSession, document_id: int, user_id: int) -> Document:
    """Get document and verify user ownership."""
    from sqlalchemy import select
    
    result = await db.execute(
        select(Document).where(
            Document.id == document_id,
            Document.owner_id == user_id
        )
    )
    document = result.scalar_one_or_none()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found or access denied"
        )
    
    return document


async def _get_document_text(document: Document) -> str:
    """Get document text content."""
    # In a real implementation, this would read the document content
    # from storage or database. For now, we'll use a placeholder.
    
    if not document.content:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Document has no text content"
        )
    
    return document.content
