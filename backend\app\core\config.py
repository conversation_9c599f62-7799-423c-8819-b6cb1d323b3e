"""Application configuration management."""

import os
from functools import lru_cache
from typing import Any, Dict, List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    # Application
    VERSION: str = "0.1.0"
    ENVIRONMENT: str = Field(default="development", description="Environment name")
    DEBUG: bool = Field(default=True, description="Debug mode")
    
    # Database
    DATABASE_URL: str = Field(
        default="postgresql://lonors:lonors_dev_password@localhost:5432/lonors",
        description="Database connection URL"
    )
    DATABASE_POOL_SIZE: int = Field(default=20, description="Database pool size")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, description="Database max overflow")
    
    # Vector Database
    QDRANT_URL: str = Field(default="http://localhost:6333", description="Qdrant URL")
    QDRANT_API_KEY: Optional[str] = Field(default=None, description="Qdrant API key")
    QDRANT_COLLECTION_NAME: str = Field(default="lonors_vectors", description="Qdrant collection name")
    
    # Cache
    REDIS_URL: str = Field(default="redis://localhost:6379", description="Redis URL")
    REDIS_DB: int = Field(default=0, description="Redis database number")
    REDIS_PASSWORD: Optional[str] = Field(default=None, description="Redis password")
    
    # Graph Database
    NEO4J_URL: str = Field(default="bolt://localhost:7687", description="Neo4j URL")
    NEO4J_USER: str = Field(default="neo4j", description="Neo4j username")
    NEO4J_PASSWORD: str = Field(default="lonors_dev_password", description="Neo4j password")
    
    # Authentication
    SECRET_KEY: str = Field(
        default="your-super-secret-key-change-this-in-production",
        description="Secret key for JWT tokens"
    )
    ALGORITHM: str = Field(default="HS256", description="JWT algorithm")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="Access token expiration")
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, description="Refresh token expiration")
    
    # OAuth
    GOOGLE_CLIENT_ID: Optional[str] = Field(default=None, description="Google OAuth client ID")
    GOOGLE_CLIENT_SECRET: Optional[str] = Field(default=None, description="Google OAuth client secret")
    GITHUB_CLIENT_ID: Optional[str] = Field(default=None, description="GitHub OAuth client ID")
    GITHUB_CLIENT_SECRET: Optional[str] = Field(default=None, description="GitHub OAuth client secret")
    
    # AI/ML
    OPENAI_API_KEY: Optional[str] = Field(default=None, description="OpenAI API key")
    ANTHROPIC_API_KEY: Optional[str] = Field(default=None, description="Anthropic API key")
    HUGGINGFACE_API_KEY: Optional[str] = Field(default=None, description="HuggingFace API key")
    OLLAMA_BASE_URL: str = Field(default="http://localhost:11434", description="Ollama base URL")
    
    # File Storage
    STORAGE_TYPE: str = Field(default="local", description="Storage type (local, s3, minio)")
    AWS_ACCESS_KEY_ID: Optional[str] = Field(default=None, description="AWS access key ID")
    AWS_SECRET_ACCESS_KEY: Optional[str] = Field(default=None, description="AWS secret access key")
    AWS_REGION: str = Field(default="us-east-1", description="AWS region")
    S3_BUCKET_NAME: Optional[str] = Field(default=None, description="S3 bucket name")
    
    MINIO_ENDPOINT: str = Field(default="localhost:9000", description="MinIO endpoint")
    MINIO_ACCESS_KEY: Optional[str] = Field(default=None, description="MinIO access key")
    MINIO_SECRET_KEY: Optional[str] = Field(default=None, description="MinIO secret key")
    MINIO_BUCKET_NAME: str = Field(default="lonors-storage", description="MinIO bucket name")
    
    # Monitoring
    SENTRY_DSN: Optional[str] = Field(default=None, description="Sentry DSN")
    PROMETHEUS_ENABLED: bool = Field(default=True, description="Enable Prometheus metrics")
    PROMETHEUS_PORT: int = Field(default=8000, description="Prometheus metrics port")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", description="Log level")
    LOG_FORMAT: str = Field(default="json", description="Log format")
    LOG_FILE: str = Field(default="logs/app.log", description="Log file path")
    
    # Celery
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/1", description="Celery broker URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/2", description="Celery result backend")
    
    # Web Scraping
    SCRAPING_DELAY: float = Field(default=1.0, description="Scraping delay between requests")
    SCRAPING_CONCURRENT_REQUESTS: int = Field(default=16, description="Concurrent scraping requests")
    SCRAPING_USER_AGENT: str = Field(default="Lonors-Bot/1.0", description="Scraping user agent")
    
    # Security
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:5500", "http://localhost:3000"],
        description="CORS allowed origins"
    )
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "127.0.0.1"],
        description="Allowed hosts"
    )
    
    # Feature Flags
    ENABLE_RAG: bool = Field(default=True, description="Enable RAG features")
    ENABLE_VOICE_TRANSCRIPTION: bool = Field(default=True, description="Enable voice transcription")
    ENABLE_WEB_CRAWLING: bool = Field(default=True, description="Enable web crawling")
    ENABLE_KNOWLEDGE_GRAPH: bool = Field(default=True, description="Enable knowledge graph")
    ENABLE_PASSWORD_MANAGER: bool = Field(default=True, description="Enable password manager")
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = Field(default=60, description="Rate limit requests per minute")
    RATE_LIMIT_BURST: int = Field(default=10, description="Rate limit burst")
    
    # Model Configuration
    DEFAULT_EMBEDDING_MODEL: str = Field(
        default="sentence-transformers/all-MiniLM-L6-v2",
        description="Default embedding model"
    )
    DEFAULT_LLM_MODEL: str = Field(default="gpt-3.5-turbo", description="Default LLM model")
    WHISPER_MODEL: str = Field(default="base", description="Whisper model size")
    
    # Development
    RELOAD: bool = Field(default=True, description="Auto-reload on code changes")
    WORKERS: int = Field(default=1, description="Number of worker processes")
    
    @validator("CORS_ORIGINS", pre=True)
    def parse_cors_origins(cls, v: Any) -> List[str]:
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v: Any) -> List[str]:
        """Parse allowed hosts from string or list."""
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.ENVIRONMENT.lower() == "production"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.ENVIRONMENT.lower() == "development"
    
    @property
    def database_config(self) -> Dict[str, Any]:
        """Get database configuration."""
        return {
            "url": self.DATABASE_URL,
            "pool_size": self.DATABASE_POOL_SIZE,
            "max_overflow": self.DATABASE_MAX_OVERFLOW,
        }


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()
