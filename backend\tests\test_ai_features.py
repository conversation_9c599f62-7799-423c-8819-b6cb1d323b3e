"""Tests for AI features service."""

import pytest
from unittest.mock import As<PERSON><PERSON>ock, MagicMock, patch

from app.services.ai_features import AIFeaturesService, ai_features_service
from app.core.exceptions import ProcessingError


class TestAIFeaturesService:
    """Test cases for AI features service."""
    
    @pytest.fixture
    def ai_service(self):
        """Create AI features service instance for testing."""
        return AIFeaturesService()
    
    @pytest.fixture
    def sample_text(self):
        """Sample text for testing."""
        return """
        This is a sample document about artificial intelligence and machine learning.
        The document discusses various aspects of AI technology including natural language processing,
        computer vision, and deep learning algorithms. Machine learning has revolutionized many industries
        including healthcare, finance, and technology. The future of AI looks promising with continued
        advancements in neural networks and computational power.
        """
    
    @pytest.fixture
    def short_text(self):
        """Short text for testing edge cases."""
        return "This is a short document."
    
    @pytest.mark.asyncio
    async def test_extractive_summarization(self, ai_service, sample_text):
        """Test extractive summarization."""
        with patch('app.services.ai_features.embedding_service') as mock_embedding:
            # Mock embedding service
            mock_embedding.generate_embeddings_batch.return_value = [
                [0.1, 0.2, 0.3] for _ in range(5)  # Mock embeddings for sentences
            ]
            
            result = await ai_service.summarize_document(
                sample_text, 
                max_length=200, 
                style="extractive"
            )
        
        assert "summary" in result
        assert "style" in result
        assert result["style"] == "extractive"
        assert "compression_ratio" in result
        assert "word_count" in result
        assert len(result["summary"]) <= 200
    
    @pytest.mark.asyncio
    async def test_abstractive_summarization(self, ai_service, sample_text):
        """Test abstractive summarization (simulated)."""
        with patch('app.services.ai_features.embedding_service') as mock_embedding:
            mock_embedding.generate_embeddings_batch.return_value = [
                [0.1, 0.2, 0.3] for _ in range(5)
            ]
            
            result = await ai_service.summarize_document(
                sample_text, 
                max_length=200, 
                style="abstractive"
            )
        
        assert "summary" in result
        assert "style" in result
        assert result["style"] == "abstractive"
        assert "key_concepts" in result
        assert "method" in result
    
    @pytest.mark.asyncio
    async def test_short_text_summarization(self, ai_service, short_text):
        """Test summarization with very short text."""
        result = await ai_service.summarize_document(
            short_text, 
            max_length=100, 
            style="extractive"
        )
        
        assert "summary" in result
        assert result["method"] == "extractive_simple"
        assert len(result["summary"]) <= len(short_text)
    
    @pytest.mark.asyncio
    async def test_supervised_categorization(self, ai_service, sample_text):
        """Test supervised document categorization."""
        categories = ["Technology", "Healthcare", "Finance", "Education"]
        
        with patch('app.services.ai_features.embedding_service') as mock_embedding:
            # Mock document embedding
            mock_embedding.generate_embedding.return_value = [0.5, 0.3, 0.8, 0.1]
            
            # Mock category embeddings
            mock_embedding.generate_embeddings_batch.return_value = [
                [0.6, 0.4, 0.7, 0.2],  # Technology
                [0.2, 0.8, 0.3, 0.5],  # Healthcare
                [0.3, 0.2, 0.4, 0.9],  # Finance
                [0.4, 0.6, 0.2, 0.3],  # Education
            ]
            
            result = await ai_service.categorize_document(sample_text, categories)
        
        assert "category" in result
        assert "confidence" in result
        assert "all_scores" in result
        assert result["category"] in categories
        assert result["method"] == "supervised_embedding"
        assert 0 <= result["confidence"] <= 1
    
    @pytest.mark.asyncio
    async def test_unsupervised_categorization(self, ai_service, sample_text):
        """Test unsupervised document categorization."""
        result = await ai_service.categorize_document(sample_text)
        
        assert "category" in result
        assert "confidence" in result
        assert "method" in result
        assert result["method"] == "unsupervised_tfidf"
        assert isinstance(result["category"], str)
    
    @pytest.mark.asyncio
    async def test_sentiment_analysis_positive(self, ai_service):
        """Test sentiment analysis with positive text."""
        positive_text = "This is an excellent document with great benefits and positive outcomes."
        
        result = await ai_service.analyze_document_sentiment(positive_text)
        
        assert "sentiment" in result
        assert "confidence" in result
        assert "positive_indicators" in result
        assert "negative_indicators" in result
        assert result["sentiment"] == "positive"
        assert result["positive_indicators"] > 0
    
    @pytest.mark.asyncio
    async def test_sentiment_analysis_negative(self, ai_service):
        """Test sentiment analysis with negative text."""
        negative_text = "This is a poor document with bad problems and negative issues."
        
        result = await ai_service.analyze_document_sentiment(negative_text)
        
        assert "sentiment" in result
        assert result["sentiment"] == "negative"
        assert result["negative_indicators"] > 0
    
    @pytest.mark.asyncio
    async def test_sentiment_analysis_neutral(self, ai_service):
        """Test sentiment analysis with neutral text."""
        neutral_text = "This document contains information about various topics and subjects."
        
        result = await ai_service.analyze_document_sentiment(neutral_text)
        
        assert "sentiment" in result
        assert result["sentiment"] == "neutral"
    
    @pytest.mark.asyncio
    async def test_entity_extraction(self, ai_service):
        """Test named entity extraction."""
        text_with_entities = """
        Contact John <NAME_EMAIL> or visit https://example.com.
        You can also call ************ or schedule a meeting on 12/25/2023.
        """
        
        result = await ai_service.extract_entities(text_with_entities)
        
        assert "entities" in result
        assert "total_entities" in result
        
        entities = result["entities"]
        assert "emails" in entities
        assert "urls" in entities
        assert "phone_numbers" in entities
        assert "dates" in entities
        
        # Check if entities were found
        assert len(entities["emails"]) > 0
        assert "<EMAIL>" in entities["emails"]
        assert len(entities["urls"]) > 0
        assert len(entities["phone_numbers"]) > 0
        assert len(entities["dates"]) > 0
    
    @pytest.mark.asyncio
    async def test_key_concept_extraction(self, ai_service, sample_text):
        """Test key concept extraction."""
        concepts = await ai_service._extract_key_concepts(sample_text)
        
        assert isinstance(concepts, list)
        assert len(concepts) > 0
        
        # Should contain AI-related terms
        concept_text = " ".join(concepts).lower()
        assert any(term in concept_text for term in ["artificial", "intelligence", "machine", "learning"])
    
    @pytest.mark.asyncio
    async def test_sentence_importance_calculation(self, ai_service):
        """Test sentence importance scoring."""
        sentences = [
            "This is the first sentence.",
            "This is the second sentence.",
            "This is the third sentence."
        ]
        
        # Mock embeddings
        embeddings = [
            [0.1, 0.2, 0.3],
            [0.4, 0.5, 0.6],
            [0.7, 0.8, 0.9]
        ]
        
        scores = await ai_service._calculate_sentence_importance(sentences, embeddings)
        
        assert len(scores) == len(sentences)
        assert all(isinstance(score, float) for score in scores)
        assert all(-1 <= score <= 1 for score in scores)  # Cosine similarity range
    
    @pytest.mark.asyncio
    async def test_sentence_selection(self, ai_service):
        """Test top sentence selection for summarization."""
        sentences = [
            "First sentence with some content.",
            "Second sentence with more content.",
            "Third sentence with additional content.",
            "Fourth sentence with extra content."
        ]
        
        scores = [0.9, 0.7, 0.8, 0.6]  # Importance scores
        max_length = 100
        
        selected = await ai_service._select_top_sentences(sentences, scores, max_length)
        
        assert len(selected) > 0
        assert all(sentence in sentences for sentence in selected)
        
        # Check that total length doesn't exceed limit
        total_length = sum(len(sentence) for sentence in selected)
        assert total_length <= max_length
    
    @pytest.mark.asyncio
    async def test_category_generation_from_terms(self, ai_service):
        """Test category generation from key terms."""
        # Test technology terms
        tech_terms = ["software", "computer", "digital", "system"]
        category = ai_service._generate_category_from_terms(tech_terms)
        assert category == "technology"
        
        # Test business terms
        business_terms = ["company", "market", "financial", "revenue"]
        category = ai_service._generate_category_from_terms(business_terms)
        assert category == "business"
        
        # Test unknown terms
        unknown_terms = ["random", "unknown", "terms"]
        category = ai_service._generate_category_from_terms(unknown_terms)
        assert category == "random"  # Should use first term
    
    @pytest.mark.asyncio
    async def test_error_handling_empty_text(self, ai_service):
        """Test error handling with empty text."""
        empty_text = ""
        
        # Summarization should handle empty text gracefully
        result = await ai_service.summarize_document(empty_text)
        assert "summary" in result
        
        # Categorization should handle empty text
        result = await ai_service.categorize_document(empty_text)
        assert "category" in result
        
        # Sentiment analysis should handle empty text
        result = await ai_service.analyze_document_sentiment(empty_text)
        assert "sentiment" in result
    
    @pytest.mark.asyncio
    async def test_error_handling_invalid_input(self, ai_service):
        """Test error handling with invalid input."""
        # Test with None input
        with pytest.raises(Exception):
            await ai_service.summarize_document(None)
    
    @pytest.mark.asyncio
    async def test_health_check(self, ai_service):
        """Test health check functionality."""
        health_status = await ai_service.health_check()
        
        assert "status" in health_status
        assert "components" in health_status
        assert health_status["status"] in ["healthy", "unhealthy"]
        
        components = health_status["components"]
        assert "summarization" in components
        assert "categorization" in components
        assert "tfidf_vectorizer" in components
    
    @pytest.mark.asyncio
    async def test_global_instance(self):
        """Test that global instance is properly initialized."""
        assert ai_features_service is not None
        assert isinstance(ai_features_service, AIFeaturesService)
    
    @pytest.mark.asyncio
    async def test_text_cleaning_and_processing(self, ai_service):
        """Test text preprocessing utilities."""
        # Test sentence splitting
        text = "First sentence. Second sentence! Third sentence?"
        sentences = ai_service._split_into_sentences(text)
        
        assert len(sentences) == 3
        assert "First sentence" in sentences[0]
        assert "Second sentence" in sentences[1]
        assert "Third sentence" in sentences[2]
    
    @pytest.mark.asyncio
    async def test_summary_refinement(self, ai_service):
        """Test summary refinement process."""
        summary = "This is a sentence. This is a similar sentence. This is a different sentence."
        max_length = 100
        
        refined = await ai_service._refine_extractive_summary(summary, max_length)
        
        assert len(refined) <= max_length
        assert isinstance(refined, str)
    
    @pytest.mark.asyncio
    async def test_concurrent_analysis(self, ai_service, sample_text):
        """Test concurrent execution of multiple analysis tasks."""
        import asyncio
        
        # Run multiple analysis tasks concurrently
        tasks = [
            ai_service.summarize_document(sample_text),
            ai_service.categorize_document(sample_text),
            ai_service.analyze_document_sentiment(sample_text),
            ai_service.extract_entities(sample_text)
        ]
        
        with patch('app.services.ai_features.embedding_service') as mock_embedding:
            mock_embedding.generate_embeddings_batch.return_value = [[0.1, 0.2, 0.3]]
            mock_embedding.generate_embedding.return_value = [0.1, 0.2, 0.3]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All tasks should complete successfully
        assert len(results) == 4
        assert all(isinstance(result, dict) for result in results)
    
    @pytest.mark.asyncio
    async def test_large_text_handling(self, ai_service):
        """Test handling of large text documents."""
        # Create a large text document
        large_text = "This is a sentence. " * 1000  # 1000 sentences
        
        with patch('app.services.ai_features.embedding_service') as mock_embedding:
            mock_embedding.generate_embeddings_batch.return_value = [
                [0.1, 0.2, 0.3] for _ in range(1000)
            ]
            
            result = await ai_service.summarize_document(large_text, max_length=500)
        
        assert "summary" in result
        assert len(result["summary"]) <= 500
        assert result["compression_ratio"] < 1.0  # Should be compressed


@pytest.mark.integration
class TestAIFeaturesIntegration:
    """Integration tests for AI features."""
    
    @pytest.mark.asyncio
    async def test_real_embedding_integration(self):
        """Test integration with real embedding service."""
        # This test would require actual embedding service setup
        pytest.skip("Requires actual embedding service setup")
    
    @pytest.mark.asyncio
    async def test_performance_benchmarks(self):
        """Test performance with various document sizes."""
        # This test would benchmark processing times
        pytest.skip("Performance test - requires benchmarking setup")
