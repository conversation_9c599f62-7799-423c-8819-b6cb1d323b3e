'use client'

import React, { useState, useEffect, useRef } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { 
  Search, 
  Filter, 
  Clock, 
  FileText, 
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Bookmark,
  BookmarkCheck,
  Loader2,
  AlertCircle,
  Sparkles
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Label } from '@/components/ui/label'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { cn } from '@/lib/utils'
import { formatDistanceToNow } from 'date-fns'

interface SearchResult {
  chunk_id: number
  document_id: number
  document_title: string
  content: string
  similarity_score: number
  chunk_index: number
  page_number?: number
  start_position?: number
  end_position?: number
}

interface SearchHistory {
  id: string
  query: string
  timestamp: string
  results_count: number
}

export function DocumentSearch() {
  const [query, setQuery] = useState('')
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false)
  const [similarityThreshold, setSimilarityThreshold] = useState([0.7])
  const [resultLimit, setResultLimit] = useState(10)
  const [documentTypes, setDocumentTypes] = useState<string[]>([])
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([])
  const [savedSearches, setSavedSearches] = useState<string[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [expandedResults, setExpandedResults] = useState<Set<number>>(new Set())
  const { toast } = useToast()
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Load search history from localStorage
  useEffect(() => {
    const history = localStorage.getItem('search-history')
    if (history) {
      setSearchHistory(JSON.parse(history))
    }
    
    const saved = localStorage.getItem('saved-searches')
    if (saved) {
      setSavedSearches(JSON.parse(saved))
    }
  }, [])

  // Search mutation
  const searchMutation = useMutation({
    mutationFn: async (searchQuery: string) => {
      const payload = {
        query: searchQuery,
        limit: resultLimit,
        threshold: similarityThreshold[0],
        document_types: documentTypes.length > 0 ? documentTypes : undefined,
      }

      const response = await fetch('/api/v1/documents/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        throw new Error('Search failed')
      }

      return response.json()
    },
    onSuccess: (results, query) => {
      setSearchResults(results)
      
      // Add to search history
      const historyItem: SearchHistory = {
        id: Date.now().toString(),
        query,
        timestamp: new Date().toISOString(),
        results_count: results.length,
      }
      
      const newHistory = [historyItem, ...searchHistory.slice(0, 9)] // Keep last 10
      setSearchHistory(newHistory)
      localStorage.setItem('search-history', JSON.stringify(newHistory))
      
      toast({
        title: 'Search Complete',
        description: `Found ${results.length} relevant chunks`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Search Failed',
        description: error instanceof Error ? error.message : 'Search failed',
        variant: 'destructive',
      })
    },
    onSettled: () => {
      setIsSearching(false)
    },
  })

  const handleSearch = (searchQuery?: string) => {
    const queryToSearch = searchQuery || query
    if (!queryToSearch.trim()) {
      toast({
        title: 'Empty Query',
        description: 'Please enter a search query',
        variant: 'destructive',
      })
      return
    }

    setIsSearching(true)
    searchMutation.mutate(queryToSearch)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const toggleExpanded = (chunkId: number) => {
    const newExpanded = new Set(expandedResults)
    if (newExpanded.has(chunkId)) {
      newExpanded.delete(chunkId)
    } else {
      newExpanded.add(chunkId)
    }
    setExpandedResults(newExpanded)
  }

  const saveSearch = (searchQuery: string) => {
    if (!savedSearches.includes(searchQuery)) {
      const newSaved = [...savedSearches, searchQuery]
      setSavedSearches(newSaved)
      localStorage.setItem('saved-searches', JSON.stringify(newSaved))
      toast({
        title: 'Search Saved',
        description: 'Search query has been saved',
      })
    }
  }

  const removeSavedSearch = (searchQuery: string) => {
    const newSaved = savedSearches.filter(s => s !== searchQuery)
    setSavedSearches(newSaved)
    localStorage.setItem('saved-searches', JSON.stringify(newSaved))
  }

  const highlightText = (text: string, query: string) => {
    if (!query.trim()) return text
    
    const words = query.toLowerCase().split(' ').filter(w => w.length > 2)
    let highlightedText = text
    
    words.forEach(word => {
      const regex = new RegExp(`(${word})`, 'gi')
      highlightedText = highlightedText.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800">$1</mark>')
    })
    
    return highlightedText
  }

  const getScoreColor = (score: number) => {
    if (score >= 0.9) return 'text-green-600 dark:text-green-400'
    if (score >= 0.8) return 'text-blue-600 dark:text-blue-400'
    if (score >= 0.7) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-gray-600 dark:text-gray-400'
  }

  return (
    <div className="space-y-6">
      {/* Search Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Semantic Search
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                ref={searchInputRef}
                placeholder="Search across your documents..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                className="pl-10"
                disabled={isSearching}
              />
            </div>
            <Button 
              onClick={() => handleSearch()}
              disabled={isSearching || !query.trim()}
              className="min-w-24"
            >
              {isSearching ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                'Search'
              )}
            </Button>
          </div>

          {/* Advanced Filters */}
          <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between">
                <span className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Advanced Filters
                </span>
                {isAdvancedOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Similarity Threshold: {similarityThreshold[0]}</Label>
                  <Slider
                    value={similarityThreshold}
                    onValueChange={setSimilarityThreshold}
                    max={1}
                    min={0.1}
                    step={0.1}
                    className="w-full"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Result Limit</Label>
                  <Select value={resultLimit.toString()} onValueChange={(v) => setResultLimit(parseInt(v))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5 results</SelectItem>
                      <SelectItem value="10">10 results</SelectItem>
                      <SelectItem value="20">20 results</SelectItem>
                      <SelectItem value="50">50 results</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Document Types</Label>
                  <Select value={documentTypes.join(',')} onValueChange={(v) => setDocumentTypes(v ? v.split(',') : [])}>
                    <SelectTrigger>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All types</SelectItem>
                      <SelectItem value="pdf">PDF only</SelectItem>
                      <SelectItem value="docx">DOCX only</SelectItem>
                      <SelectItem value="txt">TXT only</SelectItem>
                      <SelectItem value="md">Markdown only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </CardContent>
      </Card>

      {/* Search History and Saved Searches */}
      {(searchHistory.length > 0 || savedSearches.length > 0) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Search History */}
          {searchHistory.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4" />
                  Recent Searches
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {searchHistory.slice(0, 5).map((item) => (
                    <div
                      key={item.id}
                      className="flex items-center justify-between p-2 rounded hover:bg-muted cursor-pointer"
                      onClick={() => {
                        setQuery(item.query)
                        handleSearch(item.query)
                      }}
                    >
                      <div className="flex-1 min-w-0">
                        <p className="text-sm truncate">{item.query}</p>
                        <p className="text-xs text-muted-foreground">
                          {item.results_count} results • {formatDistanceToNow(new Date(item.timestamp), { addSuffix: true })}
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          saveSearch(item.query)
                        }}
                        className="h-8 w-8 p-0"
                      >
                        {savedSearches.includes(item.query) ? (
                          <BookmarkCheck className="h-4 w-4" />
                        ) : (
                          <Bookmark className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Saved Searches */}
          {savedSearches.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Bookmark className="h-4 w-4" />
                  Saved Searches
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {savedSearches.map((savedQuery) => (
                    <div
                      key={savedQuery}
                      className="flex items-center justify-between p-2 rounded hover:bg-muted cursor-pointer"
                      onClick={() => {
                        setQuery(savedQuery)
                        handleSearch(savedQuery)
                      }}
                    >
                      <p className="text-sm truncate flex-1">{savedQuery}</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          removeSavedSearch(savedQuery)
                        }}
                        className="h-8 w-8 p-0"
                      >
                        <BookmarkCheck className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Search Results */}
      {searchResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Sparkles className="h-5 w-5" />
                Search Results ({searchResults.length})
              </span>
              <Badge variant="outline">
                Query: "{query}"
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {searchResults.map((result) => {
                const isExpanded = expandedResults.has(result.chunk_id)
                const previewLength = 200
                const shouldTruncate = result.content.length > previewLength
                const displayContent = isExpanded || !shouldTruncate 
                  ? result.content 
                  : result.content.substring(0, previewLength) + '...'

                return (
                  <div
                    key={result.chunk_id}
                    className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium text-sm">{result.document_title}</h3>
                          <Badge variant="secondary" className="text-xs">
                            Chunk {result.chunk_index + 1}
                          </Badge>
                          {result.page_number && (
                            <Badge variant="outline" className="text-xs">
                              Page {result.page_number}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <span className={cn('text-sm font-medium', getScoreColor(result.similarity_score))}>
                            {Math.round(result.similarity_score * 100)}% match
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 px-2 text-xs"
                          >
                            <ExternalLink className="h-3 w-3 mr-1" />
                            View Document
                          </Button>
                        </div>
                      </div>
                    </div>
                    
                    <div className="prose prose-sm max-w-none">
                      <p 
                        className="text-sm leading-relaxed"
                        dangerouslySetInnerHTML={{ 
                          __html: highlightText(displayContent, query) 
                        }}
                      />
                    </div>
                    
                    {shouldTruncate && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(result.chunk_id)}
                        className="mt-2 h-8 px-2 text-xs"
                      >
                        {isExpanded ? (
                          <>
                            <ChevronUp className="h-3 w-3 mr-1" />
                            Show Less
                          </>
                        ) : (
                          <>
                            <ChevronDown className="h-3 w-3 mr-1" />
                            Show More
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Results */}
      {searchResults.length === 0 && query && !isSearching && searchMutation.isSuccess && (
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-lg font-medium">No results found</p>
              <p className="text-muted-foreground">Try adjusting your search query or filters</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
