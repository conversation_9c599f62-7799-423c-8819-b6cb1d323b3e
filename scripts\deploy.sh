#!/bin/bash

# Lonors RAG Platform Deployment Script
# Usage: ./scripts/deploy.sh [environment] [version]

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-staging}"
VERSION="${2:-latest}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validate environment
validate_environment() {
    case $ENVIRONMENT in
        staging|production)
            log_info "Deploying to $ENVIRONMENT environment"
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT. Use 'staging' or 'production'"
            exit 1
            ;;
    esac
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker is not running"
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check if required environment files exist
    if [[ ! -f "$PROJECT_ROOT/.env.$ENVIRONMENT" ]]; then
        log_error "Environment file .env.$ENVIRONMENT not found"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Load environment variables
load_environment() {
    log_info "Loading environment variables for $ENVIRONMENT..."
    
    # Load environment-specific variables
    set -a
    source "$PROJECT_ROOT/.env.$ENVIRONMENT"
    set +a
    
    # Validate required variables
    required_vars=(
        "POSTGRES_PASSWORD"
        "REDIS_PASSWORD"
        "SECRET_KEY"
        "JWT_SECRET_KEY"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "Required environment variable $var is not set"
            exit 1
        fi
    done
    
    log_success "Environment variables loaded"
}

# Build and push images
build_images() {
    log_info "Building Docker images..."
    
    cd "$PROJECT_ROOT"
    
    # Build backend image
    log_info "Building backend image..."
    docker build -t "lonors/backend:$VERSION" -f backend/Dockerfile backend/
    
    # Build frontend image
    log_info "Building frontend image..."
    docker build -t "lonors/frontend:$VERSION" -f frontend/Dockerfile frontend/
    
    log_success "Docker images built successfully"
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # Start only the database for migrations
    docker-compose -f docker-compose.production.yml up -d postgres
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    timeout=60
    while ! docker-compose -f docker-compose.production.yml exec -T postgres pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB"; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            log_error "Database failed to start within 60 seconds"
            exit 1
        fi
    done
    
    # Run migrations
    docker-compose -f docker-compose.production.yml run --rm backend uv run alembic upgrade head
    
    log_success "Database migrations completed"
}

# Deploy services
deploy_services() {
    log_info "Deploying services..."
    
    cd "$PROJECT_ROOT"
    
    # Pull latest images if using remote registry
    if [[ "$VERSION" != "latest" ]]; then
        log_info "Pulling images for version $VERSION..."
        docker-compose -f docker-compose.production.yml pull
    fi
    
    # Deploy with zero-downtime strategy
    log_info "Starting new containers..."
    docker-compose -f docker-compose.production.yml up -d --remove-orphans
    
    log_success "Services deployed"
}

# Health checks
run_health_checks() {
    log_info "Running health checks..."
    
    # Wait for services to be ready
    services=("backend:3000" "frontend:5500" "postgres:5432" "redis:6379" "qdrant:6333")
    
    for service in "${services[@]}"; do
        service_name="${service%:*}"
        port="${service#*:}"
        
        log_info "Checking $service_name health..."
        timeout=120
        while ! docker-compose -f docker-compose.production.yml exec -T "$service_name" nc -z localhost "$port" 2>/dev/null; do
            sleep 5
            timeout=$((timeout - 5))
            if [[ $timeout -le 0 ]]; then
                log_error "$service_name failed to start within 120 seconds"
                exit 1
            fi
        done
        log_success "$service_name is healthy"
    done
    
    # Test API endpoints
    log_info "Testing API endpoints..."
    
    # Test backend health
    if curl -f -s "http://localhost:3000/health" > /dev/null; then
        log_success "Backend health check passed"
    else
        log_error "Backend health check failed"
        exit 1
    fi
    
    # Test frontend
    if curl -f -s "http://localhost:5500" > /dev/null; then
        log_success "Frontend health check passed"
    else
        log_error "Frontend health check failed"
        exit 1
    fi
    
    log_success "All health checks passed"
}

# Run smoke tests
run_smoke_tests() {
    log_info "Running smoke tests..."
    
    # Basic API tests
    log_info "Testing document upload API..."
    
    # Create a test file
    echo "Test document content" > /tmp/test-doc.txt
    
    # Test upload (this would need authentication in real scenario)
    # upload_response=$(curl -s -X POST -F "file=@/tmp/test-doc.txt" "http://localhost:3000/api/v1/documents/upload")
    
    # Clean up
    rm -f /tmp/test-doc.txt
    
    log_success "Smoke tests completed"
}

# Cleanup old containers and images
cleanup() {
    log_info "Cleaning up old containers and images..."
    
    # Remove old containers
    docker container prune -f
    
    # Remove old images (keep last 3 versions)
    docker image prune -f
    
    log_success "Cleanup completed"
}

# Rollback function
rollback() {
    log_warning "Rolling back deployment..."
    
    # Stop current containers
    docker-compose -f docker-compose.production.yml down
    
    # Start previous version (this would need version tracking)
    # docker-compose -f docker-compose.production.yml up -d
    
    log_success "Rollback completed"
}

# Main deployment function
main() {
    log_info "Starting deployment of Lonors RAG Platform"
    log_info "Environment: $ENVIRONMENT"
    log_info "Version: $VERSION"
    
    # Trap errors and rollback
    trap 'log_error "Deployment failed. Rolling back..."; rollback; exit 1' ERR
    
    validate_environment
    check_prerequisites
    load_environment
    build_images
    run_migrations
    deploy_services
    run_health_checks
    run_smoke_tests
    cleanup
    
    log_success "Deployment completed successfully!"
    log_info "Services are available at:"
    log_info "  Frontend: http://localhost:5500"
    log_info "  Backend API: http://localhost:3000"
    log_info "  Monitoring: http://localhost:3001 (Grafana)"
    log_info "  Metrics: http://localhost:9090 (Prometheus)"
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
