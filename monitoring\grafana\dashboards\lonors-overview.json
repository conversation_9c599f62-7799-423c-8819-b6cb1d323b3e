{"dashboard": {"id": null, "title": "Lonors RAG Platform Overview", "tags": ["lonors", "rag", "overview"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "System Health", "type": "stat", "targets": [{"expr": "up{job=\"lonors-backend\"}", "legendFormat": "Backend", "refId": "A"}, {"expr": "up{job=\"lonors-frontend\"}", "legendFormat": "Frontend", "refId": "B"}, {"expr": "up{job=\"postgres\"}", "legendFormat": "Database", "refId": "C"}, {"expr": "up{job=\"qdrant\"}", "legendFormat": "Vector DB", "refId": "D"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "Down"}}, "type": "value"}, {"options": {"1": {"text": "Up"}}, "type": "value"}]}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Request Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"lonors-backend\"}[5m])", "legendFormat": "{{method}} {{endpoint}}", "refId": "A"}], "yAxes": [{"label": "Requests/sec", "min": 0}, {"show": false}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Response Time", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"lonors-backend\"}[5m]))", "legendFormat": "95th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"lonors-backend\"}[5m]))", "legendFormat": "50th percentile", "refId": "B"}], "yAxes": [{"label": "Seconds", "min": 0}, {"show": false}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Error Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"lonors-backend\",status=~\"5..\"}[5m]) / rate(http_requests_total{job=\"lonors-backend\"}[5m])", "legendFormat": "Error Rate", "refId": "A"}], "yAxes": [{"label": "Error Rate", "min": 0, "max": 1}, {"show": false}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Document Processing", "type": "stat", "targets": [{"expr": "lonors_documents_total", "legendFormat": "Total Documents", "refId": "A"}, {"expr": "lonors_documents_processing", "legendFormat": "Processing", "refId": "B"}, {"expr": "lonors_documents_processed_total", "legendFormat": "Processed Today", "refId": "C"}], "gridPos": {"h": 8, "w": 8, "x": 0, "y": 16}}, {"id": 6, "title": "Search Performance", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(lonors_search_duration_seconds_bucket[5m]))", "legendFormat": "Search Response Time (95th)", "refId": "A"}, {"expr": "rate(lonors_search_requests_total[5m])", "legendFormat": "Search Rate", "refId": "B"}], "gridPos": {"h": 8, "w": 8, "x": 8, "y": 16}}, {"id": 7, "title": "Chat Performance", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(lonors_chat_duration_seconds_bucket[5m]))", "legendFormat": "Chat Response Time (95th)", "refId": "A"}, {"expr": "rate(lonors_chat_requests_total[5m])", "legendFormat": "Chat Rate", "refId": "B"}], "gridPos": {"h": 8, "w": 8, "x": 16, "y": 16}}, {"id": 8, "title": "Resource Usage", "type": "graph", "targets": [{"expr": "rate(container_cpu_usage_seconds_total{name=~\"lonors.*\"}[5m]) * 100", "legendFormat": "{{name}} CPU %", "refId": "A"}, {"expr": "container_memory_usage_bytes{name=~\"lonors.*\"} / container_spec_memory_limit_bytes{name=~\"lonors.*\"} * 100", "legendFormat": "{{name}} Memory %", "refId": "B"}], "yAxes": [{"label": "Percentage", "min": 0, "max": 100}, {"show": false}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}]}}