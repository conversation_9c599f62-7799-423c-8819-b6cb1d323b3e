"""Health check endpoints."""

from datetime import datetime
from typing import Dict, Any

import structlog
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import get_settings
from app.core.database import get_db, db_manager

settings = get_settings()
logger = structlog.get_logger(__name__)

router = APIRouter()


@router.get("/", response_model=Dict[str, Any])
async def health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.
    
    Returns:
        Dict[str, Any]: Health status information
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
    }


@router.get("/detailed", response_model=Dict[str, Any])
async def detailed_health_check(db: AsyncSession = Depends(get_db)) -> Dict[str, Any]:
    """
    Detailed health check with dependency status.
    
    Args:
        db: Database session
    
    Returns:
        Dict[str, Any]: Detailed health status
    """
    logger.info("Performing detailed health check")
    
    # Check database health
    db_health = await db_manager.health_check()
    
    # TODO: Add checks for other services
    # - Redis/DragonflyDB
    # - Qdrant vector database
    # - Neo4j graph database
    
    health_status = {
        "status": "healthy" if db_health["status"] == "healthy" else "unhealthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
        "services": {
            "database": db_health,
            "cache": {"status": "not_implemented"},
            "vector_db": {"status": "not_implemented"},
            "graph_db": {"status": "not_implemented"},
        },
        "features": {
            "rag": settings.ENABLE_RAG,
            "voice_transcription": settings.ENABLE_VOICE_TRANSCRIPTION,
            "web_crawling": settings.ENABLE_WEB_CRAWLING,
            "knowledge_graph": settings.ENABLE_KNOWLEDGE_GRAPH,
            "password_manager": settings.ENABLE_PASSWORD_MANAGER,
        },
    }
    
    logger.info("Health check completed", status=health_status["status"])
    
    return health_status


@router.get("/readiness", response_model=Dict[str, str])
async def readiness_check(db: AsyncSession = Depends(get_db)) -> Dict[str, str]:
    """
    Kubernetes readiness probe endpoint.
    
    Args:
        db: Database session
    
    Returns:
        Dict[str, str]: Readiness status
    """
    try:
        # Check if database is accessible
        await db.execute("SELECT 1")
        
        return {"status": "ready"}
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        return {"status": "not_ready", "error": str(e)}


@router.get("/liveness", response_model=Dict[str, str])
async def liveness_check() -> Dict[str, str]:
    """
    Kubernetes liveness probe endpoint.
    
    Returns:
        Dict[str, str]: Liveness status
    """
    return {"status": "alive"}
