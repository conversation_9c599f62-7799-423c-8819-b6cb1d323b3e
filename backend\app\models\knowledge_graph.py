"""Knowledge graph database models."""

from datetime import datetime
from typing import Optional

from sqlalchemy import DateTime, Float, ForeignKey, JSON, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base


class Entity(Base):
    """Entity model for knowledge graph nodes."""
    
    __tablename__ = "entities"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Entity details
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    entity_type: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text)
    
    # Source information
    source_document_id: Mapped[Optional[int]] = mapped_column(ForeignKey("documents.id"))
    source_text: Mapped[Optional[str]] = mapped_column(Text)
    confidence_score: Mapped[Optional[float]] = mapped_column(Float)
    
    # Properties
    properties: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships
    source_document: Mapped[Optional["Document"]] = relationship("Document")
    outgoing_relationships: Mapped[list["Relationship"]] = relationship(
        "Relationship", 
        foreign_keys="Relationship.source_entity_id",
        back_populates="source_entity",
        cascade="all, delete-orphan"
    )
    incoming_relationships: Mapped[list["Relationship"]] = relationship(
        "Relationship", 
        foreign_keys="Relationship.target_entity_id",
        back_populates="target_entity",
        cascade="all, delete-orphan"
    )


class Relationship(Base):
    """Relationship model for knowledge graph edges."""
    
    __tablename__ = "relationships"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Relationship details
    source_entity_id: Mapped[int] = mapped_column(ForeignKey("entities.id"), nullable=False)
    target_entity_id: Mapped[int] = mapped_column(ForeignKey("entities.id"), nullable=False)
    relationship_type: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    
    # Relationship properties
    description: Mapped[Optional[str]] = mapped_column(Text)
    weight: Mapped[Optional[float]] = mapped_column(Float, default=1.0)
    confidence_score: Mapped[Optional[float]] = mapped_column(Float)
    
    # Source information
    source_document_id: Mapped[Optional[int]] = mapped_column(ForeignKey("documents.id"))
    source_text: Mapped[Optional[str]] = mapped_column(Text)
    
    # Properties
    properties: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships
    source_entity: Mapped["Entity"] = relationship(
        "Entity", 
        foreign_keys=[source_entity_id],
        back_populates="outgoing_relationships"
    )
    target_entity: Mapped["Entity"] = relationship(
        "Entity", 
        foreign_keys=[target_entity_id],
        back_populates="incoming_relationships"
    )
    source_document: Mapped[Optional["Document"]] = relationship("Document")


class GraphNode(Base):
    """Graph node model for Neo4j integration metadata."""
    
    __tablename__ = "graph_nodes"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Neo4j node details
    neo4j_id: Mapped[Optional[str]] = mapped_column(String(255), unique=True, index=True)
    entity_id: Mapped[int] = mapped_column(ForeignKey("entities.id"), nullable=False)
    
    # Graph properties
    labels: Mapped[list[str]] = mapped_column(JSON, nullable=False)  # Neo4j labels
    properties: Mapped[dict] = mapped_column(JSON, nullable=False)  # Neo4j properties
    
    # Synchronization
    last_synced_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    sync_status: Mapped[str] = mapped_column(String(50), default="pending", nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships
    entity: Mapped["Entity"] = relationship("Entity")
