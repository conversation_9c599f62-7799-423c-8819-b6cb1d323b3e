"""Tests for embedding service."""

from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import numpy as np
import pytest

from app.core.exceptions import ModelError, ProcessingError
from app.services.embedding_service import EmbeddingService


class TestEmbeddingService:
    """Test embedding service functionality."""
    
    @pytest.fixture
    def embedding_service(self):
        """Create embedding service instance."""
        return EmbeddingService(model_name="test-model")
    
    @pytest.fixture
    def mock_sentence_transformer(self):
        """Mock SentenceTransformer model."""
        mock_model = MagicMock()
        mock_model.encode.return_value = np.array([[0.1, 0.2, 0.3, 0.4]])
        return mock_model
    
    @pytest.mark.asyncio
    async def test_initialize_success(self, embedding_service, mock_sentence_transformer):
        """Test successful model initialization."""
        with patch('app.services.embedding_service.SentenceTransformer', return_value=mock_sentence_transformer):
            await embedding_service.initialize()
            
            assert embedding_service.is_initialized
            assert embedding_service.dimension == 4
            assert embedding_service.model is not None
    
    @pytest.mark.asyncio
    async def test_initialize_failure(self, embedding_service):
        """Test model initialization failure."""
        with patch('app.services.embedding_service.SentenceTransformer', side_effect=Exception("Model load failed")):
            with pytest.raises(ModelError, match="Failed to load embedding model"):
                await embedding_service.initialize()
    
    @pytest.mark.asyncio
    async def test_generate_embedding_success(self, embedding_service, mock_sentence_transformer):
        """Test successful embedding generation."""
        with patch('app.services.embedding_service.SentenceTransformer', return_value=mock_sentence_transformer):
            embedding = await embedding_service.generate_embedding("test text")
            
            assert isinstance(embedding, list)
            assert len(embedding) == 4
            assert embedding == [0.1, 0.2, 0.3, 0.4]
    
    @pytest.mark.asyncio
    async def test_generate_embedding_empty_text(self, embedding_service):
        """Test embedding generation with empty text."""
        with pytest.raises(ProcessingError, match="Cannot generate embedding for empty text"):
            await embedding_service.generate_embedding("")
    
    @pytest.mark.asyncio
    async def test_generate_embedding_failure(self, embedding_service, mock_sentence_transformer):
        """Test embedding generation failure."""
        mock_sentence_transformer.encode.side_effect = Exception("Encoding failed")
        
        with patch('app.services.embedding_service.SentenceTransformer', return_value=mock_sentence_transformer):
            with pytest.raises(ModelError, match="Failed to generate embedding"):
                await embedding_service.generate_embedding("test text")
    
    @pytest.mark.asyncio
    async def test_generate_embeddings_batch_success(self, embedding_service, mock_sentence_transformer):
        """Test successful batch embedding generation."""
        mock_sentence_transformer.encode.return_value = np.array([
            [0.1, 0.2, 0.3, 0.4],
            [0.5, 0.6, 0.7, 0.8],
        ])
        
        with patch('app.services.embedding_service.SentenceTransformer', return_value=mock_sentence_transformer):
            embeddings = await embedding_service.generate_embeddings_batch(
                ["text 1", "text 2"]
            )
            
            assert len(embeddings) == 2
            assert embeddings[0] == [0.1, 0.2, 0.3, 0.4]
            assert embeddings[1] == [0.5, 0.6, 0.7, 0.8]
    
    @pytest.mark.asyncio
    async def test_generate_embeddings_batch_empty_list(self, embedding_service):
        """Test batch embedding generation with empty list."""
        embeddings = await embedding_service.generate_embeddings_batch([])
        assert embeddings == []
    
    @pytest.mark.asyncio
    async def test_generate_embeddings_batch_no_valid_texts(self, embedding_service):
        """Test batch embedding generation with no valid texts."""
        with pytest.raises(ProcessingError, match="No valid texts to embed"):
            await embedding_service.generate_embeddings_batch(["", "   ", ""])
    
    @pytest.mark.asyncio
    async def test_generate_embeddings_batch_large_batch(self, embedding_service, mock_sentence_transformer):
        """Test batch embedding generation with large batch."""
        # Create 100 mock embeddings
        mock_embeddings = np.array([[0.1, 0.2] for _ in range(100)])
        mock_sentence_transformer.encode.return_value = mock_embeddings
        
        texts = [f"text {i}" for i in range(100)]
        
        with patch('app.services.embedding_service.SentenceTransformer', return_value=mock_sentence_transformer):
            embeddings = await embedding_service.generate_embeddings_batch(
                texts, batch_size=32
            )
            
            assert len(embeddings) == 100
            # Should have been called multiple times due to batching
            assert mock_sentence_transformer.encode.call_count >= 3
    
    def test_calculate_similarity_success(self, embedding_service):
        """Test successful similarity calculation."""
        embedding1 = [1.0, 0.0, 0.0]
        embedding2 = [0.0, 1.0, 0.0]
        
        similarity = embedding_service.calculate_similarity(embedding1, embedding2)
        
        # These vectors are orthogonal, so similarity should be 0.5 (normalized)
        assert 0.4 <= similarity <= 0.6
    
    def test_calculate_similarity_identical_vectors(self, embedding_service):
        """Test similarity calculation with identical vectors."""
        embedding = [1.0, 2.0, 3.0]
        
        similarity = embedding_service.calculate_similarity(embedding, embedding)
        
        # Identical vectors should have similarity of 1.0
        assert similarity == 1.0
    
    def test_calculate_similarity_different_dimensions(self, embedding_service):
        """Test similarity calculation with different dimensions."""
        embedding1 = [1.0, 0.0]
        embedding2 = [0.0, 1.0, 0.0]
        
        with pytest.raises(ProcessingError, match="Embeddings must have the same dimension"):
            embedding_service.calculate_similarity(embedding1, embedding2)
    
    def test_calculate_similarity_zero_vectors(self, embedding_service):
        """Test similarity calculation with zero vectors."""
        embedding1 = [0.0, 0.0, 0.0]
        embedding2 = [1.0, 2.0, 3.0]
        
        similarity = embedding_service.calculate_similarity(embedding1, embedding2)
        
        # Zero vector should have similarity of 0.0
        assert similarity == 0.0
    
    def test_find_most_similar_success(self, embedding_service):
        """Test finding most similar embeddings."""
        query_embedding = [1.0, 0.0, 0.0]
        candidates = [
            ("id1", [1.0, 0.0, 0.0]),  # Identical
            ("id2", [0.0, 1.0, 0.0]),  # Orthogonal
            ("id3", [0.5, 0.5, 0.0]),  # Somewhat similar
        ]
        
        results = embedding_service.find_most_similar(
            query_embedding, candidates, threshold=0.5, limit=2
        )
        
        # Should return the most similar ones above threshold
        assert len(results) <= 2
        assert results[0][0] == "id1"  # Most similar should be first
        assert results[0][1] == 1.0    # Perfect similarity
    
    def test_find_most_similar_empty_candidates(self, embedding_service):
        """Test finding similar embeddings with empty candidates."""
        query_embedding = [1.0, 0.0, 0.0]
        
        results = embedding_service.find_most_similar(query_embedding, [])
        
        assert results == []
    
    def test_find_most_similar_no_matches_above_threshold(self, embedding_service):
        """Test finding similar embeddings with no matches above threshold."""
        query_embedding = [1.0, 0.0, 0.0]
        candidates = [
            ("id1", [0.0, 1.0, 0.0]),  # Orthogonal
            ("id2", [0.0, 0.0, 1.0]),  # Orthogonal
        ]
        
        results = embedding_service.find_most_similar(
            query_embedding, candidates, threshold=0.9
        )
        
        assert results == []
    
    @pytest.mark.asyncio
    async def test_get_model_info(self, embedding_service, mock_sentence_transformer):
        """Test getting model information."""
        mock_sentence_transformer.max_seq_length = 512
        
        with patch('app.services.embedding_service.SentenceTransformer', return_value=mock_sentence_transformer):
            model_info = await embedding_service.get_model_info()
            
            assert model_info["model_name"] == "test-model"
            assert model_info["dimension"] == 4
            assert model_info["max_sequence_length"] == 512
            assert model_info["is_initialized"] is True
    
    def test_properties(self, embedding_service):
        """Test service properties."""
        # Before initialization
        assert not embedding_service.is_initialized
        assert embedding_service.dimension is None
        
        # After setting properties manually (simulating initialization)
        embedding_service.model = MagicMock()
        embedding_service.embedding_dimension = 384
        
        assert embedding_service.is_initialized
        assert embedding_service.dimension == 384
    
    @pytest.mark.asyncio
    async def test_concurrent_initialization(self, embedding_service, mock_sentence_transformer):
        """Test concurrent initialization doesn't cause issues."""
        with patch('app.services.embedding_service.SentenceTransformer', return_value=mock_sentence_transformer):
            # Start multiple initialization tasks
            tasks = [
                embedding_service.initialize(),
                embedding_service.initialize(),
                embedding_service.initialize(),
            ]
            
            # Wait for all to complete
            await asyncio.gather(*tasks)
            
            # Should only initialize once
            assert embedding_service.is_initialized
            assert embedding_service.model is not None
