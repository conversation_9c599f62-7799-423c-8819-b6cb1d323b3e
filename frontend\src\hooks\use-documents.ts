/**
 * React Query hooks for document management
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import { 
  documentAPI, 
  documentQueryKeys,
  type Document,
  type DocumentStats,
  type DocumentProcessingStatus,
  type SearchQuery,
  type SearchResult,
  type ChatResponse
} from '@/lib/api/documents'

/**
 * Hook to fetch all documents
 */
export function useDocuments(filters?: {
  search?: string
  status?: string
  document_type?: string
}) {
  return useQuery({
    queryKey: documentQueryKeys.list(filters || {}),
    queryFn: () => documentAPI.getDocuments(filters),
    staleTime: 30000, // 30 seconds
    refetchInterval: 5000, // Refetch every 5 seconds for status updates
  })
}

/**
 * Hook to fetch a specific document
 */
export function useDocument(documentId: number) {
  return useQuery({
    queryKey: documentQueryKeys.detail(documentId),
    queryFn: () => documentAPI.getDocument(documentId),
    enabled: !!documentId,
  })
}

/**
 * Hook to fetch document statistics
 */
export function useDocumentStats() {
  return useQuery({
    queryKey: documentQueryKeys.stats(),
    queryFn: () => documentAPI.getDocumentStats(),
    staleTime: 60000, // 1 minute
  })
}

/**
 * Hook to fetch document processing status
 */
export function useDocumentStatus(documentId: number) {
  return useQuery({
    queryKey: documentQueryKeys.status(documentId),
    queryFn: () => documentAPI.getDocumentStatus(documentId),
    enabled: !!documentId,
    refetchInterval: (data) => {
      // Stop refetching if document is processed or failed
      if (data?.status === 'processed' || data?.status === 'failed') {
        return false
      }
      return 2000 // Refetch every 2 seconds while processing
    },
  })
}

/**
 * Hook to fetch document chunks
 */
export function useDocumentChunks(documentId: number) {
  return useQuery({
    queryKey: documentQueryKeys.chunks(documentId),
    queryFn: () => documentAPI.getDocumentChunks(documentId),
    enabled: !!documentId,
  })
}

/**
 * Hook to fetch similar documents
 */
export function useSimilarDocuments(documentId: number, limit: number = 5) {
  return useQuery({
    queryKey: documentQueryKeys.similar(documentId),
    queryFn: () => documentAPI.getSimilarDocuments(documentId, limit),
    enabled: !!documentId,
  })
}

/**
 * Hook to upload documents
 */
export function useUploadDocument() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: ({ 
      file, 
      metadata 
    }: { 
      file: File
      metadata?: { title?: string; tags?: string }
    }) => documentAPI.uploadDocument(file, metadata),
    onSuccess: (document) => {
      // Invalidate and refetch documents list
      queryClient.invalidateQueries({ queryKey: documentQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: documentQueryKeys.stats() })
      
      toast({
        title: 'Upload Successful',
        description: `${document.filename} uploaded successfully`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Upload Failed',
        description: error instanceof Error ? error.message : 'Upload failed',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to delete a document
 */
export function useDeleteDocument() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: (documentId: number) => documentAPI.deleteDocument(documentId),
    onSuccess: (_, documentId) => {
      // Remove from cache and invalidate lists
      queryClient.removeQueries({ queryKey: documentQueryKeys.detail(documentId) })
      queryClient.invalidateQueries({ queryKey: documentQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: documentQueryKeys.stats() })
      
      toast({
        title: 'Document Deleted',
        description: 'Document has been successfully deleted',
      })
    },
    onError: (error) => {
      toast({
        title: 'Delete Failed',
        description: error instanceof Error ? error.message : 'Failed to delete document',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update document metadata
 */
export function useUpdateDocument() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: ({ 
      documentId, 
      updates 
    }: { 
      documentId: number
      updates: { title?: string; tags?: string[] }
    }) => documentAPI.updateDocument(documentId, updates),
    onSuccess: (document, { documentId }) => {
      // Update cache
      queryClient.setQueryData(documentQueryKeys.detail(documentId), document)
      queryClient.invalidateQueries({ queryKey: documentQueryKeys.lists() })
      
      toast({
        title: 'Document Updated',
        description: 'Document has been successfully updated',
      })
    },
    onError: (error) => {
      toast({
        title: 'Update Failed',
        description: error instanceof Error ? error.message : 'Failed to update document',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to reprocess a document
 */
export function useReprocessDocument() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: (documentId: number) => documentAPI.reprocessDocument(documentId),
    onSuccess: (document, documentId) => {
      // Update cache
      queryClient.setQueryData(documentQueryKeys.detail(documentId), document)
      queryClient.invalidateQueries({ queryKey: documentQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: documentQueryKeys.status(documentId) })
      
      toast({
        title: 'Reprocessing Started',
        description: 'Document reprocessing has been initiated',
      })
    },
    onError: (error) => {
      toast({
        title: 'Reprocess Failed',
        description: error instanceof Error ? error.message : 'Failed to reprocess document',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to search documents
 */
export function useSearchDocuments() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: (query: SearchQuery) => documentAPI.searchDocuments(query),
    onError: (error) => {
      toast({
        title: 'Search Failed',
        description: error instanceof Error ? error.message : 'Search failed',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to chat with documents
 */
export function useChatWithDocuments() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: ({ 
      query, 
      contextLimit = 5 
    }: { 
      query: string
      contextLimit?: number 
    }) => documentAPI.chatWithDocuments(query, contextLimit),
    onError: (error) => {
      toast({
        title: 'Chat Failed',
        description: error instanceof Error ? error.message : 'Chat request failed',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to download a document
 */
export function useDownloadDocument() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: (documentId: number) => documentAPI.downloadDocument(documentId),
    onSuccess: (blob, documentId) => {
      // Create download link
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `document-${documentId}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast({
        title: 'Download Started',
        description: 'Document download has started',
      })
    },
    onError: (error) => {
      toast({
        title: 'Download Failed',
        description: error instanceof Error ? error.message : 'Failed to download document',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to bulk delete documents
 */
export function useBulkDeleteDocuments() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: (documentIds: number[]) => documentAPI.bulkDeleteDocuments(documentIds),
    onSuccess: (_, documentIds) => {
      // Remove from cache and invalidate lists
      documentIds.forEach(id => {
        queryClient.removeQueries({ queryKey: documentQueryKeys.detail(id) })
      })
      queryClient.invalidateQueries({ queryKey: documentQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: documentQueryKeys.stats() })
      
      toast({
        title: 'Documents Deleted',
        description: `${documentIds.length} documents have been deleted`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Bulk Delete Failed',
        description: error instanceof Error ? error.message : 'Failed to delete documents',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to export documents
 */
export function useExportDocuments() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: ({ 
      documentIds, 
      format 
    }: { 
      documentIds: number[]
      format: 'zip' | 'pdf' 
    }) => documentAPI.exportDocuments(documentIds, format),
    onSuccess: (blob, { documentIds, format }) => {
      // Create download link
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `documents-export.${format}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast({
        title: 'Export Complete',
        description: `${documentIds.length} documents exported as ${format.toUpperCase()}`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Export Failed',
        description: error instanceof Error ? error.message : 'Failed to export documents',
        variant: 'destructive',
      })
    },
  })
}
