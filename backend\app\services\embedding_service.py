"""Embedding service for generating text embeddings."""

import asyncio
from typing import List, Optional, <PERSON>ple

import numpy as np
import structlog
from sentence_transformers import SentenceTransformer

from app.core.config import get_settings
from app.core.exceptions import ModelError, ProcessingError

settings = get_settings()
logger = structlog.get_logger(__name__)


class EmbeddingService:
    """Service for generating text embeddings using sentence transformers."""
    
    def __init__(self, model_name: Optional[str] = None):
        """
        Initialize embedding service.
        
        Args:
            model_name: Name of the sentence transformer model to use
        """
        self.model_name = model_name or settings.DEFAULT_EMBEDDING_MODEL
        self.model: Optional[SentenceTransformer] = None
        self.embedding_dimension: Optional[int] = None
        self._model_lock = asyncio.Lock()
    
    async def initialize(self) -> None:
        """Initialize the embedding model."""
        async with self._model_lock:
            if self.model is None:
                logger.info("Loading embedding model", model_name=self.model_name)
                
                try:
                    # Load model in thread pool to avoid blocking
                    loop = asyncio.get_event_loop()
                    self.model = await loop.run_in_executor(
                        None, 
                        SentenceTransformer, 
                        self.model_name
                    )
                    
                    # Get embedding dimension
                    test_embedding = self.model.encode(["test"])
                    self.embedding_dimension = test_embedding.shape[1]
                    
                    logger.info(
                        "Embedding model loaded successfully",
                        model_name=self.model_name,
                        dimension=self.embedding_dimension,
                    )
                    
                except Exception as e:
                    logger.error("Failed to load embedding model", error=str(e))
                    raise ModelError(f"Failed to load embedding model: {str(e)}")
    
    async def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for a single text.
        
        Args:
            text: Text to embed
            
        Returns:
            List[float]: Text embedding vector
            
        Raises:
            ModelError: If embedding generation fails
        """
        if not text.strip():
            raise ProcessingError("Cannot generate embedding for empty text")
        
        await self.initialize()
        
        try:
            logger.debug("Generating embedding", text_length=len(text))
            
            # Generate embedding in thread pool
            loop = asyncio.get_event_loop()
            embedding = await loop.run_in_executor(
                None,
                self.model.encode,
                [text]
            )
            
            # Convert to list and return first (and only) embedding
            embedding_list = embedding[0].tolist()
            
            logger.debug(
                "Embedding generated successfully",
                dimension=len(embedding_list),
            )
            
            return embedding_list
            
        except Exception as e:
            logger.error("Embedding generation failed", error=str(e))
            raise ModelError(f"Failed to generate embedding: {str(e)}")
    
    async def generate_embeddings_batch(
        self, 
        texts: List[str], 
        batch_size: int = 32
    ) -> List[List[float]]:
        """
        Generate embeddings for multiple texts in batches.
        
        Args:
            texts: List of texts to embed
            batch_size: Number of texts to process in each batch
            
        Returns:
            List[List[float]]: List of embedding vectors
            
        Raises:
            ModelError: If embedding generation fails
        """
        if not texts:
            return []
        
        # Filter out empty texts
        valid_texts = [text for text in texts if text.strip()]
        if not valid_texts:
            raise ProcessingError("No valid texts to embed")
        
        await self.initialize()
        
        try:
            logger.info(
                "Generating embeddings batch",
                total_texts=len(valid_texts),
                batch_size=batch_size,
            )
            
            all_embeddings = []
            
            # Process in batches
            for i in range(0, len(valid_texts), batch_size):
                batch_texts = valid_texts[i:i + batch_size]
                
                logger.debug(
                    "Processing batch",
                    batch_start=i,
                    batch_size=len(batch_texts),
                )
                
                # Generate embeddings for batch in thread pool
                loop = asyncio.get_event_loop()
                batch_embeddings = await loop.run_in_executor(
                    None,
                    self.model.encode,
                    batch_texts
                )
                
                # Convert to list of lists
                batch_embeddings_list = [
                    embedding.tolist() for embedding in batch_embeddings
                ]
                
                all_embeddings.extend(batch_embeddings_list)
                
                # Small delay between batches to prevent overwhelming
                if i + batch_size < len(valid_texts):
                    await asyncio.sleep(0.1)
            
            logger.info(
                "Batch embedding generation completed",
                total_embeddings=len(all_embeddings),
            )
            
            return all_embeddings
            
        except Exception as e:
            logger.error("Batch embedding generation failed", error=str(e))
            raise ModelError(f"Failed to generate batch embeddings: {str(e)}")
    
    def calculate_similarity(
        self, 
        embedding1: List[float], 
        embedding2: List[float]
    ) -> float:
        """
        Calculate cosine similarity between two embeddings.
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            float: Cosine similarity score (0-1)
            
        Raises:
            ProcessingError: If embeddings are invalid
        """
        if len(embedding1) != len(embedding2):
            raise ProcessingError("Embeddings must have the same dimension")
        
        try:
            # Convert to numpy arrays
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # Calculate cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            
            # Ensure result is in [0, 1] range
            similarity = max(0.0, min(1.0, (similarity + 1) / 2))
            
            return float(similarity)
            
        except Exception as e:
            logger.error("Similarity calculation failed", error=str(e))
            raise ProcessingError(f"Failed to calculate similarity: {str(e)}")
    
    def find_most_similar(
        self, 
        query_embedding: List[float], 
        candidate_embeddings: List[Tuple[str, List[float]]], 
        threshold: float = 0.7,
        limit: int = 10
    ) -> List[Tuple[str, float]]:
        """
        Find most similar embeddings to a query embedding.
        
        Args:
            query_embedding: Query embedding vector
            candidate_embeddings: List of (id, embedding) tuples
            threshold: Minimum similarity threshold
            limit: Maximum number of results to return
            
        Returns:
            List[Tuple[str, float]]: List of (id, similarity_score) tuples
        """
        if not candidate_embeddings:
            return []
        
        try:
            similarities = []
            
            for candidate_id, candidate_embedding in candidate_embeddings:
                similarity = self.calculate_similarity(
                    query_embedding, 
                    candidate_embedding
                )
                
                if similarity >= threshold:
                    similarities.append((candidate_id, similarity))
            
            # Sort by similarity (descending) and limit results
            similarities.sort(key=lambda x: x[1], reverse=True)
            return similarities[:limit]
            
        except Exception as e:
            logger.error("Similarity search failed", error=str(e))
            raise ProcessingError(f"Failed to find similar embeddings: {str(e)}")
    
    @property
    def is_initialized(self) -> bool:
        """Check if the model is initialized."""
        return self.model is not None
    
    @property
    def dimension(self) -> Optional[int]:
        """Get the embedding dimension."""
        return self.embedding_dimension
    
    async def get_model_info(self) -> dict:
        """
        Get information about the current model.
        
        Returns:
            dict: Model information
        """
        await self.initialize()
        
        return {
            "model_name": self.model_name,
            "dimension": self.embedding_dimension,
            "max_sequence_length": getattr(self.model, "max_seq_length", None),
            "is_initialized": self.is_initialized,
        }


# Global embedding service instance
embedding_service = EmbeddingService()
