"""Multi-modal document processing service for images, audio, and complex PDFs."""

import asyncio
import io
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import cv2
import easyocr
import librosa
import numpy as np
import pytesseract
import soundfile as sf
import whisper
from pdf2image import convert_from_bytes, convert_from_path
from PIL import Image
from pydub import AudioSegment
import structlog

from app.core.config import get_settings
from app.core.exceptions import ProcessingError

settings = get_settings()
logger = structlog.get_logger(__name__)


class MultiModalProcessor:
    """Service for processing multi-modal content including images, audio, and complex documents."""
    
    def __init__(self):
        """Initialize multi-modal processor."""
        self.ocr_reader = None
        self.whisper_model = None
        self._initialize_models()
    
    def _initialize_models(self) -> None:
        """Initialize OCR and speech recognition models."""
        try:
            # Initialize EasyOCR (supports multiple languages)
            self.ocr_reader = easyocr.Reader(['en', 'es', 'fr', 'de', 'it'])
            
            # Initialize Whisper model
            self.whisper_model = whisper.load_model(settings.WHISPER_MODEL)
            
            logger.info("Multi-modal models initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize multi-modal models", error=str(e))
            raise ProcessingError(f"Failed to initialize multi-modal models: {str(e)}")
    
    async def process_pdf_with_images(
        self, 
        pdf_content: bytes, 
        extract_images: bool = True
    ) -> Dict:
        """
        Process PDF with image extraction and OCR.
        
        Args:
            pdf_content: PDF file content as bytes
            extract_images: Whether to extract and process images
            
        Returns:
            Dict: Processing results with text and image data
        """
        logger.info("Processing PDF with image extraction")
        
        try:
            # Convert PDF pages to images
            pages = convert_from_bytes(pdf_content, dpi=300)
            
            results = {
                "pages": [],
                "extracted_images": [],
                "total_text": "",
                "image_count": 0,
                "page_count": len(pages)
            }
            
            for page_num, page_image in enumerate(pages, 1):
                page_result = await self._process_pdf_page(
                    page_image, page_num, extract_images
                )
                results["pages"].append(page_result)
                results["total_text"] += page_result["text"] + "\n\n"
                
                if extract_images:
                    results["extracted_images"].extend(page_result["images"])
                    results["image_count"] += len(page_result["images"])
            
            logger.info(
                "PDF processing completed",
                pages=len(pages),
                images_extracted=results["image_count"]
            )
            
            return results
            
        except Exception as e:
            logger.error("PDF processing failed", error=str(e))
            raise ProcessingError(f"PDF processing failed: {str(e)}")
    
    async def _process_pdf_page(
        self, 
        page_image: Image.Image, 
        page_num: int, 
        extract_images: bool
    ) -> Dict:
        """Process a single PDF page."""
        page_result = {
            "page_number": page_num,
            "text": "",
            "images": [],
            "image_regions": []
        }
        
        # Convert PIL Image to numpy array for OpenCV
        page_array = np.array(page_image)
        
        # Extract text using OCR
        page_result["text"] = await self._extract_text_from_image(page_image)
        
        if extract_images:
            # Detect and extract image regions
            image_regions = await self._detect_image_regions(page_array)
            page_result["image_regions"] = image_regions
            
            # Extract images from detected regions
            for region in image_regions:
                extracted_image = await self._extract_image_region(
                    page_array, region, page_num
                )
                if extracted_image:
                    page_result["images"].append(extracted_image)
        
        return page_result
    
    async def _extract_text_from_image(self, image: Image.Image) -> str:
        """Extract text from image using OCR."""
        try:
            # Use EasyOCR for better accuracy
            image_array = np.array(image)
            results = self.ocr_reader.readtext(image_array)
            
            # Combine all detected text
            text_parts = []
            for (bbox, text, confidence) in results:
                if confidence > 0.5:  # Filter low-confidence results
                    text_parts.append(text)
            
            return " ".join(text_parts)
            
        except Exception as e:
            logger.warning("OCR extraction failed, falling back to Tesseract", error=str(e))
            
            # Fallback to Tesseract
            try:
                return pytesseract.image_to_string(image)
            except Exception as fallback_error:
                logger.error("All OCR methods failed", error=str(fallback_error))
                return ""
    
    async def _detect_image_regions(self, page_array: np.ndarray) -> List[Dict]:
        """Detect image regions in a PDF page."""
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(page_array, cv2.COLOR_RGB2GRAY)
            
            # Apply threshold to get binary image
            _, binary = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY_INV)
            
            # Find contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            image_regions = []
            for contour in contours:
                # Filter by area (remove small noise)
                area = cv2.contourArea(contour)
                if area > 10000:  # Minimum area threshold
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Filter by aspect ratio (likely to be images)
                    aspect_ratio = w / h
                    if 0.3 < aspect_ratio < 3.0:  # Reasonable aspect ratios
                        image_regions.append({
                            "x": int(x),
                            "y": int(y),
                            "width": int(w),
                            "height": int(h),
                            "area": int(area)
                        })
            
            return image_regions
            
        except Exception as e:
            logger.warning("Image region detection failed", error=str(e))
            return []
    
    async def _extract_image_region(
        self, 
        page_array: np.ndarray, 
        region: Dict, 
        page_num: int
    ) -> Optional[Dict]:
        """Extract and process an image region."""
        try:
            x, y, w, h = region["x"], region["y"], region["width"], region["height"]
            
            # Extract region
            image_region = page_array[y:y+h, x:x+w]
            
            # Convert to PIL Image
            pil_image = Image.fromarray(image_region)
            
            # Extract text from image if it contains any
            image_text = await self._extract_text_from_image(pil_image)
            
            # Save image to temporary location (in production, save to storage)
            with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_file:
                pil_image.save(tmp_file.name)
                
                return {
                    "page_number": page_num,
                    "region": region,
                    "text": image_text,
                    "image_path": tmp_file.name,
                    "format": "png"
                }
                
        except Exception as e:
            logger.warning("Image region extraction failed", error=str(e))
            return None
    
    async def process_audio_file(
        self, 
        audio_content: bytes, 
        file_format: str = "wav"
    ) -> Dict:
        """
        Process audio file and extract text using speech recognition.
        
        Args:
            audio_content: Audio file content as bytes
            file_format: Audio file format (wav, mp3, m4a, etc.)
            
        Returns:
            Dict: Processing results with transcribed text and metadata
        """
        logger.info("Processing audio file", format=file_format)
        
        try:
            # Create temporary file for audio processing
            with tempfile.NamedTemporaryFile(suffix=f".{file_format}") as tmp_file:
                tmp_file.write(audio_content)
                tmp_file.flush()
                
                # Convert to WAV if necessary
                audio_path = await self._convert_audio_to_wav(tmp_file.name, file_format)
                
                # Load audio with librosa
                audio_data, sample_rate = librosa.load(audio_path, sr=16000)
                
                # Get audio metadata
                duration = len(audio_data) / sample_rate
                
                # Transcribe with Whisper
                result = self.whisper_model.transcribe(audio_path)
                
                # Clean up temporary files
                if audio_path != tmp_file.name:
                    Path(audio_path).unlink(missing_ok=True)
                
                return {
                    "text": result["text"],
                    "language": result.get("language", "unknown"),
                    "duration_seconds": duration,
                    "sample_rate": sample_rate,
                    "segments": result.get("segments", []),
                    "confidence": self._calculate_average_confidence(result.get("segments", []))
                }
                
        except Exception as e:
            logger.error("Audio processing failed", error=str(e))
            raise ProcessingError(f"Audio processing failed: {str(e)}")
    
    async def _convert_audio_to_wav(self, input_path: str, input_format: str) -> str:
        """Convert audio file to WAV format if necessary."""
        if input_format.lower() == "wav":
            return input_path
        
        try:
            # Load audio with pydub
            audio = AudioSegment.from_file(input_path, format=input_format)
            
            # Convert to WAV
            wav_path = input_path.replace(f".{input_format}", ".wav")
            audio.export(wav_path, format="wav")
            
            return wav_path
            
        except Exception as e:
            logger.error("Audio conversion failed", error=str(e))
            raise ProcessingError(f"Audio conversion failed: {str(e)}")
    
    def _calculate_average_confidence(self, segments: List[Dict]) -> float:
        """Calculate average confidence from Whisper segments."""
        if not segments:
            return 0.0
        
        # Whisper doesn't always provide confidence scores
        # This is a placeholder for when they're available
        confidences = []
        for segment in segments:
            if "confidence" in segment:
                confidences.append(segment["confidence"])
        
        return sum(confidences) / len(confidences) if confidences else 0.8  # Default confidence
    
    async def process_image_file(
        self, 
        image_content: bytes, 
        file_format: str = "png"
    ) -> Dict:
        """
        Process standalone image file and extract text.
        
        Args:
            image_content: Image file content as bytes
            file_format: Image file format
            
        Returns:
            Dict: Processing results with extracted text and metadata
        """
        logger.info("Processing image file", format=file_format)
        
        try:
            # Load image
            image = Image.open(io.BytesIO(image_content))
            
            # Get image metadata
            width, height = image.size
            mode = image.mode
            
            # Extract text using OCR
            extracted_text = await self._extract_text_from_image(image)
            
            # Analyze image content (basic analysis)
            analysis = await self._analyze_image_content(image)
            
            return {
                "text": extracted_text,
                "width": width,
                "height": height,
                "mode": mode,
                "format": file_format,
                "analysis": analysis,
                "has_text": len(extracted_text.strip()) > 0
            }
            
        except Exception as e:
            logger.error("Image processing failed", error=str(e))
            raise ProcessingError(f"Image processing failed: {str(e)}")
    
    async def _analyze_image_content(self, image: Image.Image) -> Dict:
        """Perform basic image content analysis."""
        try:
            # Convert to numpy array
            image_array = np.array(image)
            
            # Basic statistics
            if len(image_array.shape) == 3:  # Color image
                mean_color = np.mean(image_array, axis=(0, 1))
                dominant_color = mean_color.tolist()
            else:  # Grayscale
                mean_brightness = np.mean(image_array)
                dominant_color = [mean_brightness, mean_brightness, mean_brightness]
            
            # Detect if image is mostly text (high contrast, lots of edges)
            gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY) if len(image_array.shape) == 3 else image_array
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            
            return {
                "dominant_color": dominant_color,
                "edge_density": float(edge_density),
                "likely_text_image": edge_density > 0.1,  # High edge density suggests text
                "brightness": float(np.mean(gray)),
                "contrast": float(np.std(gray))
            }
            
        except Exception as e:
            logger.warning("Image analysis failed", error=str(e))
            return {
                "dominant_color": [128, 128, 128],
                "edge_density": 0.0,
                "likely_text_image": False,
                "brightness": 128.0,
                "contrast": 0.0
            }
    
    async def get_supported_formats(self) -> Dict[str, List[str]]:
        """Get list of supported file formats for multi-modal processing."""
        return {
            "images": ["png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp"],
            "audio": ["wav", "mp3", "m4a", "flac", "ogg", "aac"],
            "documents": ["pdf"],  # PDFs with image extraction
        }
    
    async def health_check(self) -> Dict:
        """Check health of multi-modal processing components."""
        health_status = {
            "status": "healthy",
            "components": {}
        }
        
        try:
            # Check OCR
            if self.ocr_reader is not None:
                health_status["components"]["ocr"] = "healthy"
            else:
                health_status["components"]["ocr"] = "unhealthy"
                health_status["status"] = "unhealthy"
            
            # Check Whisper
            if self.whisper_model is not None:
                health_status["components"]["whisper"] = "healthy"
            else:
                health_status["components"]["whisper"] = "unhealthy"
                health_status["status"] = "unhealthy"
            
            # Test basic functionality
            test_image = Image.new('RGB', (100, 100), color='white')
            test_text = await self._extract_text_from_image(test_image)
            health_status["components"]["ocr_test"] = "passed"
            
        except Exception as e:
            logger.error("Multi-modal health check failed", error=str(e))
            health_status["status"] = "unhealthy"
            health_status["error"] = str(e)
        
        return health_status


# Global instance
multimodal_processor = MultiModalProcessor()
