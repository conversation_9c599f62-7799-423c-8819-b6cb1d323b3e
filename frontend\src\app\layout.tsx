import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/providers/theme-provider'
import { QueryProvider } from '@/components/providers/query-provider'
import { Toaster } from '@/components/ui/toaster'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Lonors AI Agent Platform',
  description: 'Enterprise-grade AI agent platform with RAG, LangGraph, and multi-modal capabilities',
  keywords: ['AI', 'agents', 'RAG', 'LangGraph', 'enterprise'],
  authors: [{ name: 'Lonors Team' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'Lonors AI Agent Platform',
    description: 'Enterprise-grade AI agent platform with RAG, LangGraph, and multi-modal capabilities',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Lonors AI Agent Platform',
    description: 'Enterprise-grade AI agent platform with RAG, LangGraph, and multi-modal capabilities',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <QueryProvider>
            {children}
            <Toaster />
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
