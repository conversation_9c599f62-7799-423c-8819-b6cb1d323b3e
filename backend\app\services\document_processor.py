"""Document processing service for text extraction and chunking."""

import hashlib
import mimetypes
import os
import re
from pathlib import Path
from typing import List, Optional, Tuple

import aiofiles
import magic
import structlog
from docx import Document as DocxDocument
from pypdf import PdfReader

from app.core.config import get_settings
from app.core.exceptions import ProcessingError, ValidationError
from app.models.document import DocumentTypeEnum

settings = get_settings()
logger = structlog.get_logger(__name__)


class DocumentProcessor:
    """Service for processing and extracting text from documents."""
    
    # Supported file types and their MIME types
    SUPPORTED_TYPES = {
        DocumentTypeEnum.PDF: ["application/pdf"],
        DocumentTypeEnum.DOCX: [
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        ],
        DocumentTypeEnum.TXT: ["text/plain"],
        DocumentTypeEnum.MD: ["text/markdown", "text/x-markdown"],
    }
    
    # Maximum file sizes (in bytes)
    MAX_FILE_SIZES = {
        DocumentTypeEnum.PDF: 50 * 1024 * 1024,  # 50MB
        DocumentTypeEnum.DOCX: 25 * 1024 * 1024,  # 25MB
        DocumentTypeEnum.TXT: 10 * 1024 * 1024,  # 10MB
        DocumentTypeEnum.MD: 10 * 1024 * 1024,  # 10MB
    }
    
    def __init__(self, storage_path: str = "storage/documents"):
        """
        Initialize document processor.
        
        Args:
            storage_path: Path to store uploaded documents
        """
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # Chunking configuration
        self.chunk_size = 1000  # characters
        self.chunk_overlap = 200  # characters
        self.min_chunk_size = 100  # minimum chunk size
    
    async def validate_file(
        self, 
        file_content: bytes, 
        filename: str
    ) -> Tuple[DocumentTypeEnum, str]:
        """
        Validate uploaded file.
        
        Args:
            file_content: File content bytes
            filename: Original filename
            
        Returns:
            Tuple[DocumentTypeEnum, str]: Document type and MIME type
            
        Raises:
            ValidationError: If file is invalid
        """
        logger.info("Validating file", filename=filename, size=len(file_content))
        
        # Check file size
        if len(file_content) == 0:
            raise ValidationError("File is empty")
        
        if len(file_content) > 100 * 1024 * 1024:  # 100MB absolute limit
            raise ValidationError("File too large (maximum 100MB)")
        
        # Detect MIME type
        try:
            mime_type = magic.from_buffer(file_content, mime=True)
        except Exception as e:
            logger.warning("Failed to detect MIME type", error=str(e))
            # Fallback to filename extension
            mime_type, _ = mimetypes.guess_type(filename)
            if not mime_type:
                raise ValidationError("Unable to determine file type")
        
        # Determine document type
        doc_type = None
        for dtype, mime_types in self.SUPPORTED_TYPES.items():
            if mime_type in mime_types:
                doc_type = dtype
                break
        
        if not doc_type:
            # Try to infer from file extension
            ext = Path(filename).suffix.lower()
            if ext == ".pdf":
                doc_type = DocumentTypeEnum.PDF
                mime_type = "application/pdf"
            elif ext == ".docx":
                doc_type = DocumentTypeEnum.DOCX
                mime_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            elif ext == ".txt":
                doc_type = DocumentTypeEnum.TXT
                mime_type = "text/plain"
            elif ext in [".md", ".markdown"]:
                doc_type = DocumentTypeEnum.MD
                mime_type = "text/markdown"
            else:
                raise ValidationError(f"Unsupported file type: {mime_type}")
        
        # Check file size limits
        max_size = self.MAX_FILE_SIZES.get(doc_type, 10 * 1024 * 1024)
        if len(file_content) > max_size:
            raise ValidationError(
                f"File too large for {doc_type.value} (maximum {max_size // (1024*1024)}MB)"
            )
        
        # Basic virus scanning (check for suspicious patterns)
        await self._basic_virus_scan(file_content, filename)
        
        logger.info(
            "File validation successful",
            filename=filename,
            doc_type=doc_type.value,
            mime_type=mime_type,
            size=len(file_content),
        )
        
        return doc_type, mime_type
    
    async def _basic_virus_scan(self, file_content: bytes, filename: str) -> None:
        """
        Basic virus scanning using pattern matching.
        
        Args:
            file_content: File content bytes
            filename: Original filename
            
        Raises:
            ValidationError: If suspicious content detected
        """
        # Check for suspicious file extensions in content
        suspicious_patterns = [
            b"<script",
            b"javascript:",
            b"vbscript:",
            b"onload=",
            b"onerror=",
            b"eval(",
            b"document.write",
        ]
        
        content_lower = file_content.lower()
        for pattern in suspicious_patterns:
            if pattern in content_lower:
                logger.warning(
                    "Suspicious content detected",
                    filename=filename,
                    pattern=pattern.decode('utf-8', errors='ignore'),
                )
                raise ValidationError("File contains suspicious content")
    
    async def save_file(
        self, 
        file_content: bytes, 
        filename: str, 
        user_id: int
    ) -> Tuple[str, str]:
        """
        Save uploaded file to storage.
        
        Args:
            file_content: File content bytes
            filename: Original filename
            user_id: User ID for organization
            
        Returns:
            Tuple[str, str]: File path and content hash
        """
        # Generate content hash
        content_hash = hashlib.sha256(file_content).hexdigest()
        
        # Create user directory
        user_dir = self.storage_path / str(user_id)
        user_dir.mkdir(exist_ok=True)
        
        # Generate unique filename
        file_ext = Path(filename).suffix
        safe_filename = re.sub(r'[^\w\-_.]', '_', Path(filename).stem)
        unique_filename = f"{content_hash[:8]}_{safe_filename}{file_ext}"
        file_path = user_dir / unique_filename
        
        # Save file
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)
        
        logger.info(
            "File saved successfully",
            filename=filename,
            file_path=str(file_path),
            content_hash=content_hash,
            user_id=user_id,
        )
        
        return str(file_path), content_hash
    
    async def extract_text(
        self, 
        file_path: str, 
        doc_type: DocumentTypeEnum
    ) -> str:
        """
        Extract text from document.
        
        Args:
            file_path: Path to document file
            doc_type: Document type
            
        Returns:
            str: Extracted text content
            
        Raises:
            ProcessingError: If text extraction fails
        """
        logger.info("Extracting text", file_path=file_path, doc_type=doc_type.value)
        
        try:
            if doc_type == DocumentTypeEnum.PDF:
                return await self._extract_pdf_text(file_path)
            elif doc_type == DocumentTypeEnum.DOCX:
                return await self._extract_docx_text(file_path)
            elif doc_type == DocumentTypeEnum.TXT:
                return await self._extract_txt_text(file_path)
            elif doc_type == DocumentTypeEnum.MD:
                return await self._extract_markdown_text(file_path)
            else:
                raise ProcessingError(f"Unsupported document type: {doc_type}")
                
        except Exception as e:
            logger.error("Text extraction failed", error=str(e), file_path=file_path)
            raise ProcessingError(f"Failed to extract text: {str(e)}")
    
    async def _extract_pdf_text(self, file_path: str) -> str:
        """Extract text from PDF file."""
        text_parts = []
        
        with open(file_path, 'rb') as file:
            reader = PdfReader(file)
            
            for page_num, page in enumerate(reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text_parts.append(page_text)
                except Exception as e:
                    logger.warning(
                        "Failed to extract text from PDF page",
                        page_num=page_num,
                        error=str(e),
                    )
        
        return "\n\n".join(text_parts)
    
    async def _extract_docx_text(self, file_path: str) -> str:
        """Extract text from DOCX file."""
        doc = DocxDocument(file_path)
        text_parts = []
        
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text_parts.append(paragraph.text)
        
        return "\n\n".join(text_parts)
    
    async def _extract_txt_text(self, file_path: str) -> str:
        """Extract text from TXT file."""
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as file:
            return await file.read()
    
    async def _extract_markdown_text(self, file_path: str) -> str:
        """Extract text from Markdown file."""
        import markdown
        from bs4 import BeautifulSoup
        
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as file:
            md_content = await file.read()
        
        # Convert markdown to HTML then extract text
        html = markdown.markdown(md_content)
        soup = BeautifulSoup(html, 'html.parser')
        return soup.get_text()
    
    def create_chunks(self, text: str, metadata: Optional[dict] = None) -> List[dict]:
        """
        Split text into chunks for vector embedding.
        
        Args:
            text: Text content to chunk
            metadata: Optional metadata to include with chunks
            
        Returns:
            List[dict]: List of text chunks with metadata
        """
        logger.info(
            "Creating text chunks",
            text_length=len(text),
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
        )
        
        if not text.strip():
            return []
        
        # Clean and normalize text
        text = self._clean_text(text)
        
        chunks = []
        start = 0
        chunk_index = 0
        
        while start < len(text):
            # Calculate end position
            end = start + self.chunk_size
            
            # If this is not the last chunk, try to break at sentence boundary
            if end < len(text):
                # Look for sentence endings within the last 200 characters
                search_start = max(start + self.chunk_size - 200, start)
                sentence_end = self._find_sentence_boundary(text, search_start, end)
                if sentence_end > start:
                    end = sentence_end
            
            chunk_text = text[start:end].strip()
            
            # Skip chunks that are too small
            if len(chunk_text) >= self.min_chunk_size:
                chunk_data = {
                    "content": chunk_text,
                    "chunk_index": chunk_index,
                    "start_position": start,
                    "end_position": end,
                    "character_count": len(chunk_text),
                    "token_count": self._estimate_token_count(chunk_text),
                }
                
                if metadata:
                    chunk_data.update(metadata)
                
                chunks.append(chunk_data)
                chunk_index += 1
            
            # Move start position with overlap
            start = max(end - self.chunk_overlap, start + 1)
        
        logger.info("Text chunking completed", chunk_count=len(chunks))
        return chunks
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove control characters
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        # Normalize line breaks
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        return text.strip()
    
    def _find_sentence_boundary(self, text: str, start: int, end: int) -> int:
        """Find the best sentence boundary within the given range."""
        # Look for sentence endings (., !, ?)
        sentence_endings = ['.', '!', '?']
        
        best_pos = end
        for i in range(end - 1, start - 1, -1):
            if text[i] in sentence_endings:
                # Check if this looks like a real sentence ending
                if i + 1 < len(text) and (text[i + 1].isspace() or text[i + 1].isupper()):
                    best_pos = i + 1
                    break
        
        return best_pos
    
    def _estimate_token_count(self, text: str) -> int:
        """Estimate token count for text (rough approximation)."""
        # Simple estimation: ~4 characters per token on average
        return len(text) // 4
