"""Document-related database models for RAG system."""

import enum
from datetime import datetime
from typing import Optional

from sqlalchemy import DateTime, Enum, Float, Foreign<PERSON>ey, Integer, JSO<PERSON>, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base


class DocumentTypeEnum(str, enum.Enum):
    """Document type enumeration."""
    PDF = "pdf"
    DOCX = "docx"
    TXT = "txt"
    MD = "md"
    HTML = "html"
    CSV = "csv"
    JSON = "json"
    XML = "xml"
    AUDIO = "audio"
    VIDEO = "video"
    IMAGE = "image"


class DocumentStatusEnum(str, enum.Enum):
    """Document processing status enumeration."""
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"
    ARCHIVED = "archived"


class Document(Base):
    """Document model for RAG system."""
    
    __tablename__ = "documents"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Basic information
    title: Mapped[str] = mapped_column(String(500), nullable=False)
    filename: Mapped[str] = mapped_column(String(255), nullable=False)
    file_path: Mapped[str] = mapped_column(String(1000), nullable=False)
    file_size: Mapped[int] = mapped_column(Integer, nullable=False)  # Size in bytes
    
    # Document type and format
    document_type: Mapped[DocumentTypeEnum] = mapped_column(
        Enum(DocumentTypeEnum), 
        nullable=False
    )
    mime_type: Mapped[str] = mapped_column(String(100), nullable=False)
    
    # Content
    content: Mapped[Optional[str]] = mapped_column(Text)
    content_hash: Mapped[str] = mapped_column(String(64), nullable=False, index=True)  # SHA-256
    
    # Processing status
    status: Mapped[DocumentStatusEnum] = mapped_column(
        Enum(DocumentStatusEnum), 
        default=DocumentStatusEnum.UPLOADED, 
        nullable=False
    )
    processing_error: Mapped[Optional[str]] = mapped_column(Text)
    
    # Ownership
    owner_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    
    # Metadata
    language: Mapped[Optional[str]] = mapped_column(String(10))  # ISO language code
    tags: Mapped[Optional[list[str]]] = mapped_column(JSON)
    custom_metadata: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Vector embedding info
    embedding_model: Mapped[Optional[str]] = mapped_column(String(100))
    embedding_dimensions: Mapped[Optional[int]] = mapped_column(Integer)
    chunk_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    processed_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Relationships
    owner: Mapped["User"] = relationship("User", back_populates="documents")
    chunks: Mapped[list["DocumentChunk"]] = relationship(
        "DocumentChunk", 
        back_populates="document",
        cascade="all, delete-orphan"
    )
    metadata_entries: Mapped[list["DocumentMetadata"]] = relationship(
        "DocumentMetadata", 
        back_populates="document",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Document(id={self.id}, title='{self.title}', status='{self.status}')>"
    
    @property
    def is_processed(self) -> bool:
        """Check if document is successfully processed."""
        return self.status == DocumentStatusEnum.PROCESSED
    
    @property
    def file_size_mb(self) -> float:
        """Get file size in megabytes."""
        return round(self.file_size / (1024 * 1024), 2)


class DocumentChunk(Base):
    """Document chunk model for vector storage."""
    
    __tablename__ = "document_chunks"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Document reference
    document_id: Mapped[int] = mapped_column(ForeignKey("documents.id"), nullable=False)
    
    # Chunk information
    chunk_index: Mapped[int] = mapped_column(Integer, nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    content_hash: Mapped[str] = mapped_column(String(64), nullable=False, index=True)
    
    # Position in document
    start_position: Mapped[Optional[int]] = mapped_column(Integer)
    end_position: Mapped[Optional[int]] = mapped_column(Integer)
    page_number: Mapped[Optional[int]] = mapped_column(Integer)
    
    # Vector embedding
    vector_id: Mapped[Optional[str]] = mapped_column(String(100), index=True)  # ID in vector DB
    embedding_model: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Chunk metadata
    token_count: Mapped[Optional[int]] = mapped_column(Integer)
    character_count: Mapped[int] = mapped_column(Integer, nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships
    document: Mapped["Document"] = relationship("Document", back_populates="chunks")
    
    def __repr__(self) -> str:
        return f"<DocumentChunk(id={self.id}, document_id={self.document_id}, index={self.chunk_index})>"


class DocumentMetadata(Base):
    """Document metadata model for additional information."""
    
    __tablename__ = "document_metadata"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Document reference
    document_id: Mapped[int] = mapped_column(ForeignKey("documents.id"), nullable=False)
    
    # Metadata
    key: Mapped[str] = mapped_column(String(100), nullable=False)
    value: Mapped[str] = mapped_column(Text, nullable=False)
    value_type: Mapped[str] = mapped_column(String(50), default="string", nullable=False)
    
    # Source information
    extracted_by: Mapped[Optional[str]] = mapped_column(String(100))  # Tool/method used
    confidence_score: Mapped[Optional[float]] = mapped_column(Float)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships
    document: Mapped["Document"] = relationship("Document", back_populates="metadata_entries")
    
    def __repr__(self) -> str:
        return f"<DocumentMetadata(id={self.id}, key='{self.key}', value='{self.value[:50]}...')>"
    
    @property
    def typed_value(self):
        """Get value converted to appropriate type."""
        if self.value_type == "int":
            return int(self.value)
        elif self.value_type == "float":
            return float(self.value)
        elif self.value_type == "bool":
            return self.value.lower() in ("true", "1", "yes")
        elif self.value_type == "json":
            import json
            return json.loads(self.value)
        else:
            return self.value
