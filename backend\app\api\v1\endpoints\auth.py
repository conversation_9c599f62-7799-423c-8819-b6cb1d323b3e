"""Authentication endpoints."""

from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict

import structlog
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.api.dependencies import get_current_user
from app.core.config import get_settings
from app.core.database import get_db
from app.core.exceptions import AuthenticationError, ConflictError, NotFoundError
from app.core.security import (
    create_access_token,
    create_refresh_token,
    generate_password_reset_token,
    get_password_hash,
    verify_password,
    verify_password_reset_token,
    verify_token,
)
from app.models.user import User
from app.schemas.auth import (
    ChangePassword,
    PasswordReset,
    PasswordResetRequest,
    RefreshToken,
    Token,
    UserLogin,
    UserRegister,
)
from app.schemas.user import User as UserSchema

settings = get_settings()
logger = structlog.get_logger(__name__)

router = APIRouter()


@router.post("/register", response_model=UserSchema, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserRegister,
    db: AsyncSession = Depends(get_db),
) -> UserSchema:
    """
    Register a new user.
    
    Args:
        user_data: User registration data
        db: Database session
    
    Returns:
        UserSchema: Created user information
    
    Raises:
        HTTPException: If registration fails
    """
    logger.info("User registration attempt", email=user_data.email, username=user_data.username)
    
    try:
        # Check if email already exists
        result = await db.execute(select(User).where(User.email == user_data.email))
        if result.scalar_one_or_none():
            raise ConflictError("Email already registered")
        
        # Check if username already exists
        result = await db.execute(select(User).where(User.username == user_data.username))
        if result.scalar_one_or_none():
            raise ConflictError("Username already taken")
        
        # Create new user
        hashed_password = get_password_hash(user_data.password)
        
        new_user = User(
            email=user_data.email,
            username=user_data.username,
            hashed_password=hashed_password,
            full_name=user_data.full_name,
            is_active=True,
            is_verified=False,  # Require email verification
        )
        
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        
        logger.info("User registered successfully", user_id=new_user.id, email=new_user.email)
        
        # Convert to schema
        user_schema = UserSchema.from_orm(new_user)
        user_schema.permissions = new_user.permissions
        
        return user_schema
        
    except (ConflictError, AuthenticationError) as e:
        logger.warning("Registration failed", error=str(e), email=user_data.email)
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error("Registration error", error=str(e), email=user_data.email)
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=Token)
async def login(
    user_data: UserLogin,
    db: AsyncSession = Depends(get_db),
) -> Token:
    """
    Authenticate user and return JWT tokens.
    
    Args:
        user_data: User login credentials
        db: Database session
    
    Returns:
        Token: Access and refresh tokens
    
    Raises:
        HTTPException: If authentication fails
    """
    logger.info("Login attempt", email=user_data.email)
    
    try:
        # Get user by email
        result = await db.execute(select(User).where(User.email == user_data.email))
        user = result.scalar_one_or_none()
        
        if not user:
            raise AuthenticationError("Invalid email or password")
        
        # Verify password
        if not verify_password(user_data.password, user.hashed_password):
            raise AuthenticationError("Invalid email or password")
        
        # Check if user is active
        if not user.is_active:
            raise AuthenticationError("Account is disabled")
        
        # Create tokens
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        if user_data.remember_me:
            access_token_expires = timedelta(days=7)  # Extended expiration
        
        access_token = create_access_token(
            subject=user.id,
            expires_delta=access_token_expires,
            additional_claims={
                "username": user.username,
                "role": user.role.value,
                "permissions": user.permissions,
            }
        )
        
        refresh_token = create_refresh_token(subject=user.id)
        
        # Update last login
        from datetime import datetime
        user.last_login_at = datetime.utcnow()
        await db.commit()
        
        logger.info("Login successful", user_id=user.id, email=user.email)
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=int(access_token_expires.total_seconds()),
        )
        
    except AuthenticationError as e:
        logger.warning("Login failed", error=str(e), email=user_data.email)
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error("Login error", error=str(e), email=user_data.email)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/refresh", response_model=Token)
async def refresh_token(
    token_data: RefreshToken,
    db: AsyncSession = Depends(get_db),
) -> Token:
    """
    Refresh access token using refresh token.
    
    Args:
        token_data: Refresh token data
        db: Database session
    
    Returns:
        Token: New access and refresh tokens
    
    Raises:
        HTTPException: If token refresh fails
    """
    try:
        # Verify refresh token
        payload = verify_token(token_data.refresh_token, token_type="refresh")
        user_id = payload.get("sub")
        
        if not user_id:
            raise AuthenticationError("Invalid refresh token")
        
        # Get user
        result = await db.execute(select(User).where(User.id == int(user_id)))
        user = result.scalar_one_or_none()
        
        if not user or not user.is_active:
            raise AuthenticationError("User not found or inactive")
        
        # Create new tokens
        access_token = create_access_token(
            subject=user.id,
            additional_claims={
                "username": user.username,
                "role": user.role.value,
                "permissions": user.permissions,
            }
        )
        
        new_refresh_token = create_refresh_token(subject=user.id)
        
        logger.info("Token refreshed successfully", user_id=user.id)
        
        return Token(
            access_token=access_token,
            refresh_token=new_refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        )
        
    except AuthenticationError as e:
        logger.warning("Token refresh failed", error=str(e))
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error("Token refresh error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/password-reset-request", response_model=Dict[str, str])
async def request_password_reset(
    request_data: PasswordResetRequest,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, str]:
    """
    Request password reset token.
    
    Args:
        request_data: Password reset request data
        db: Database session
    
    Returns:
        Dict[str, str]: Success message
    """
    logger.info("Password reset requested", email=request_data.email)
    
    try:
        # Get user by email
        result = await db.execute(select(User).where(User.email == request_data.email))
        user = result.scalar_one_or_none()
        
        if user and user.is_active:
            # Generate reset token
            reset_token = generate_password_reset_token(user.email)
            
            # Store token in database (in production, send via email)
            from datetime import datetime, timedelta
            user.password_reset_token = reset_token
            user.password_reset_expires = datetime.utcnow() + timedelta(hours=1)
            await db.commit()
            
            logger.info("Password reset token generated", user_id=user.id, email=user.email)
            
            # TODO: Send email with reset token
            # For development, we'll return the token in the response
            if settings.DEBUG:
                return {"message": "Password reset token generated", "token": reset_token}
        
        # Always return success to prevent email enumeration
        return {"message": "If the email exists, a password reset link has been sent"}
        
    except Exception as e:
        logger.error("Password reset request error", error=str(e), email=request_data.email)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset request failed"
        )


@router.post("/password-reset", response_model=Dict[str, str])
async def reset_password(
    reset_data: PasswordReset,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, str]:
    """
    Reset password using reset token.
    
    Args:
        reset_data: Password reset data
        db: Database session
    
    Returns:
        Dict[str, str]: Success message
    
    Raises:
        HTTPException: If password reset fails
    """
    try:
        # Verify reset token
        email = verify_password_reset_token(reset_data.token)
        if not email:
            raise AuthenticationError("Invalid or expired reset token")
        
        # Get user
        result = await db.execute(select(User).where(User.email == email))
        user = result.scalar_one_or_none()
        
        if not user:
            raise NotFoundError("User not found")
        
        # Check if token matches and is not expired
        from datetime import datetime
        if (user.password_reset_token != reset_data.token or 
            not user.password_reset_expires or 
            user.password_reset_expires < datetime.utcnow()):
            raise AuthenticationError("Invalid or expired reset token")
        
        # Update password
        user.hashed_password = get_password_hash(reset_data.new_password)
        user.password_reset_token = None
        user.password_reset_expires = None
        await db.commit()
        
        logger.info("Password reset successful", user_id=user.id, email=user.email)
        
        return {"message": "Password reset successful"}
        
    except (AuthenticationError, NotFoundError) as e:
        logger.warning("Password reset failed", error=str(e))
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error("Password reset error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )


@router.post("/change-password", response_model=Dict[str, str])
async def change_password(
    password_data: ChangePassword,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, str]:
    """
    Change user password.
    
    Args:
        password_data: Password change data
        current_user: Current authenticated user
        db: Database session
    
    Returns:
        Dict[str, str]: Success message
    
    Raises:
        HTTPException: If password change fails
    """
    try:
        # Verify current password
        if not verify_password(password_data.current_password, current_user.hashed_password):
            raise AuthenticationError("Current password is incorrect")
        
        # Update password
        current_user.hashed_password = get_password_hash(password_data.new_password)
        await db.commit()
        
        logger.info("Password changed successfully", user_id=current_user.id)
        
        return {"message": "Password changed successfully"}
        
    except AuthenticationError as e:
        logger.warning("Password change failed", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error("Password change error", error=str(e), user_id=current_user.id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed"
        )


@router.get("/me", response_model=UserSchema)
async def get_current_user_info(
    current_user: User = Depends(get_current_user),
) -> UserSchema:
    """
    Get current user information.
    
    Args:
        current_user: Current authenticated user
    
    Returns:
        UserSchema: Current user information
    """
    user_schema = UserSchema.from_orm(current_user)
    user_schema.permissions = current_user.permissions
    return user_schema
