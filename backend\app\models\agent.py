"""Agent-related database models."""

import enum
from datetime import datetime
from typing import Optional

from sqlalchemy import DateT<PERSON>, Enum, ForeignKey, JSON, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base


class AgentStatusEnum(str, enum.Enum):
    """Agent status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    ARCHIVED = "archived"


class Agent(Base):
    """Agent model for LangGraph agents."""
    
    __tablename__ = "agents"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Basic information
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    
    # Agent configuration
    agent_type: Mapped[str] = mapped_column(String(100), nullable=False)  # e.g., "conversational", "task_based"
    model_name: Mapped[str] = mapped_column(String(255), nullable=False)
    system_prompt: Mapped[Optional[str]] = mapped_column(Text)
    
    # Status
    status: Mapped[AgentStatusEnum] = mapped_column(
        Enum(AgentStatusEnum), 
        default=AgentStatusEnum.DRAFT, 
        nullable=False
    )
    
    # Ownership
    owner_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    
    # Configuration
    configuration: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships
    owner: Mapped["User"] = relationship("User", back_populates="agents")
    executions: Mapped[list["AgentExecution"]] = relationship(
        "AgentExecution", 
        back_populates="agent",
        cascade="all, delete-orphan"
    )
    configurations: Mapped[list["AgentConfiguration"]] = relationship(
        "AgentConfiguration", 
        back_populates="agent",
        cascade="all, delete-orphan"
    )


class AgentExecution(Base):
    """Agent execution model for tracking agent runs."""
    
    __tablename__ = "agent_executions"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Agent reference
    agent_id: Mapped[int] = mapped_column(ForeignKey("agents.id"), nullable=False)
    
    # Execution details
    input_data: Mapped[dict] = mapped_column(JSON, nullable=False)
    output_data: Mapped[Optional[dict]] = mapped_column(JSON)
    error_message: Mapped[Optional[str]] = mapped_column(Text)
    
    # Status
    status: Mapped[str] = mapped_column(String(50), nullable=False)  # running, completed, failed
    
    # Timestamps
    started_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Relationships
    agent: Mapped["Agent"] = relationship("Agent", back_populates="executions")


class AgentConfiguration(Base):
    """Agent configuration model for versioned configurations."""
    
    __tablename__ = "agent_configurations"
    
    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Agent reference
    agent_id: Mapped[int] = mapped_column(ForeignKey("agents.id"), nullable=False)
    
    # Configuration details
    version: Mapped[str] = mapped_column(String(50), nullable=False)
    configuration: Mapped[dict] = mapped_column(JSON, nullable=False)
    is_active: Mapped[bool] = mapped_column(default=False, nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    agent: Mapped["Agent"] = relationship("Agent", back_populates="configurations")
