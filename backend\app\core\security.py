"""Security utilities for authentication and authorization."""

import secrets
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union

import structlog
from jose import JWTError, jwt
from passlib.context import CryptContext

from app.core.config import get_settings
from app.core.exceptions import AuthenticationError, AuthorizationError

settings = get_settings()
logger = structlog.get_logger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_access_token(
    subject: Union[str, Any],
    expires_delta: Optional[timedelta] = None,
    additional_claims: Optional[Dict[str, Any]] = None,
) -> str:
    """
    Create a JWT access token.
    
    Args:
        subject: The subject (usually user ID) for the token
        expires_delta: Token expiration time
        additional_claims: Additional claims to include in the token
    
    Returns:
        str: Encoded JWT token
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "type": "access",
        "iat": datetime.utcnow(),
    }
    
    if additional_claims:
        to_encode.update(additional_claims)
    
    try:
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        logger.info("Access token created", subject=subject, expires_at=expire.isoformat())
        return encoded_jwt
    except Exception as e:
        logger.error("Failed to create access token", error=str(e), subject=subject)
        raise AuthenticationError("Failed to create access token")


def create_refresh_token(subject: Union[str, Any]) -> str:
    """
    Create a JWT refresh token.
    
    Args:
        subject: The subject (usually user ID) for the token
    
    Returns:
        str: Encoded JWT refresh token
    """
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "type": "refresh",
        "iat": datetime.utcnow(),
    }
    
    try:
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        logger.info("Refresh token created", subject=subject, expires_at=expire.isoformat())
        return encoded_jwt
    except Exception as e:
        logger.error("Failed to create refresh token", error=str(e), subject=subject)
        raise AuthenticationError("Failed to create refresh token")


def verify_token(token: str, token_type: str = "access") -> Dict[str, Any]:
    """
    Verify and decode a JWT token.
    
    Args:
        token: The JWT token to verify
        token_type: Expected token type ("access" or "refresh")
    
    Returns:
        Dict[str, Any]: Decoded token payload
    
    Raises:
        AuthenticationError: If token is invalid or expired
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        
        # Verify token type
        if payload.get("type") != token_type:
            raise AuthenticationError(f"Invalid token type. Expected {token_type}")
        
        # Verify expiration
        exp = payload.get("exp")
        if exp is None:
            raise AuthenticationError("Token missing expiration")
        
        if datetime.utcnow() > datetime.fromtimestamp(exp):
            raise AuthenticationError("Token has expired")
        
        logger.debug("Token verified successfully", subject=payload.get("sub"), type=token_type)
        return payload
        
    except JWTError as e:
        logger.warning("JWT verification failed", error=str(e), token_type=token_type)
        raise AuthenticationError("Invalid token")
    except Exception as e:
        logger.error("Token verification error", error=str(e), token_type=token_type)
        raise AuthenticationError("Token verification failed")


def get_password_hash(password: str) -> str:
    """
    Hash a password using bcrypt.
    
    Args:
        password: Plain text password
    
    Returns:
        str: Hashed password
    """
    try:
        hashed = pwd_context.hash(password)
        logger.debug("Password hashed successfully")
        return hashed
    except Exception as e:
        logger.error("Password hashing failed", error=str(e))
        raise AuthenticationError("Failed to hash password")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against its hash.
    
    Args:
        plain_password: Plain text password
        hashed_password: Hashed password to verify against
    
    Returns:
        bool: True if password matches, False otherwise
    """
    try:
        result = pwd_context.verify(plain_password, hashed_password)
        logger.debug("Password verification completed", result=result)
        return result
    except Exception as e:
        logger.error("Password verification failed", error=str(e))
        return False


def generate_password_reset_token(email: str) -> str:
    """
    Generate a password reset token.
    
    Args:
        email: User email address
    
    Returns:
        str: Password reset token
    """
    expire = datetime.utcnow() + timedelta(hours=1)  # 1 hour expiration
    
    to_encode = {
        "exp": expire,
        "sub": email,
        "type": "password_reset",
        "iat": datetime.utcnow(),
    }
    
    try:
        token = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        logger.info("Password reset token generated", email=email)
        return token
    except Exception as e:
        logger.error("Failed to generate password reset token", error=str(e), email=email)
        raise AuthenticationError("Failed to generate password reset token")


def verify_password_reset_token(token: str) -> Optional[str]:
    """
    Verify a password reset token and return the email.
    
    Args:
        token: Password reset token
    
    Returns:
        Optional[str]: Email if token is valid, None otherwise
    """
    try:
        payload = verify_token(token, token_type="password_reset")
        email = payload.get("sub")
        logger.info("Password reset token verified", email=email)
        return email
    except AuthenticationError:
        logger.warning("Invalid password reset token")
        return None


def generate_api_key() -> str:
    """
    Generate a secure API key.
    
    Returns:
        str: Generated API key
    """
    api_key = secrets.token_urlsafe(32)
    logger.info("API key generated")
    return api_key


def check_permissions(user_permissions: list[str], required_permissions: list[str]) -> bool:
    """
    Check if user has required permissions.
    
    Args:
        user_permissions: List of user's permissions
        required_permissions: List of required permissions
    
    Returns:
        bool: True if user has all required permissions
    """
    if not required_permissions:
        return True
    
    has_permissions = all(perm in user_permissions for perm in required_permissions)
    
    logger.debug(
        "Permission check",
        user_permissions=user_permissions,
        required_permissions=required_permissions,
        result=has_permissions,
    )
    
    return has_permissions


def check_role_hierarchy(user_role: str, required_role: str) -> bool:
    """
    Check if user role meets the required role level.
    
    Args:
        user_role: User's role
        required_role: Required role level
    
    Returns:
        bool: True if user role is sufficient
    """
    role_hierarchy = {
        "user": 1,
        "moderator": 2,
        "admin": 3,
        "superuser": 4,
    }
    
    user_level = role_hierarchy.get(user_role, 0)
    required_level = role_hierarchy.get(required_role, 0)
    
    has_access = user_level >= required_level
    
    logger.debug(
        "Role hierarchy check",
        user_role=user_role,
        required_role=required_role,
        user_level=user_level,
        required_level=required_level,
        result=has_access,
    )
    
    return has_access


class SecurityValidator:
    """Security validation utilities."""
    
    @staticmethod
    def validate_password_strength(password: str) -> tuple[bool, list[str]]:
        """
        Validate password strength.
        
        Args:
            password: Password to validate
        
        Returns:
            tuple[bool, list[str]]: (is_valid, list_of_issues)
        """
        issues = []
        
        if len(password) < 8:
            issues.append("Password must be at least 8 characters long")
        
        if not any(c.isupper() for c in password):
            issues.append("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in password):
            issues.append("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in password):
            issues.append("Password must contain at least one digit")
        
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            issues.append("Password must contain at least one special character")
        
        is_valid = len(issues) == 0
        
        logger.debug("Password strength validation", is_valid=is_valid, issues_count=len(issues))
        
        return is_valid, issues
    
    @staticmethod
    def validate_email_format(email: str) -> bool:
        """
        Validate email format.
        
        Args:
            email: Email to validate
        
        Returns:
            bool: True if email format is valid
        """
        import re
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        is_valid = re.match(pattern, email) is not None
        
        logger.debug("Email format validation", email=email, is_valid=is_valid)
        
        return is_valid
