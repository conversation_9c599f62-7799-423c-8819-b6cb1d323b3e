import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { DocumentUpload } from '../DocumentUpload'

// Mock the toast hook
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}))

// Mock fetch
global.fetch = jest.fn()

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('DocumentUpload', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => 'mock-token'),
        setItem: jest.fn(),
        removeItem: jest.fn(),
      },
      writable: true,
    })
  })

  it('renders upload area correctly', () => {
    render(<DocumentUpload />, { wrapper: createWrapper() })
    
    expect(screen.getByText('Upload Documents')).toBeInTheDocument()
    expect(screen.getByText('Drag & drop files here')).toBeInTheDocument()
    expect(screen.getByText('or click to browse files')).toBeInTheDocument()
  })

  it('shows supported file formats', () => {
    render(<DocumentUpload />, { wrapper: createWrapper() })
    
    expect(screen.getByText('.pdf (max 50MB)')).toBeInTheDocument()
    expect(screen.getByText('.docx (max 25MB)')).toBeInTheDocument()
    expect(screen.getByText('.txt (max 10MB)')).toBeInTheDocument()
    expect(screen.getByText('.md (max 10MB)')).toBeInTheDocument()
  })

  it('handles file drop correctly', async () => {
    const user = userEvent.setup()
    render(<DocumentUpload />, { wrapper: createWrapper() })
    
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
    const dropzone = screen.getByText('Drag & drop files here').closest('div')
    
    if (dropzone) {
      await user.upload(dropzone.querySelector('input[type="file"]')!, file)
    }
    
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
    })
  })

  it('shows file metadata inputs when files are selected', async () => {
    const user = userEvent.setup()
    render(<DocumentUpload />, { wrapper: createWrapper() })
    
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
    const input = screen.getByRole('button').closest('div')?.querySelector('input[type="file"]')
    
    if (input) {
      await user.upload(input, file)
    }
    
    await waitFor(() => {
      expect(screen.getByLabelText('Document Title (Optional)')).toBeInTheDocument()
      expect(screen.getByLabelText('Tags (Optional)')).toBeInTheDocument()
    })
  })

  it('handles successful upload', async () => {
    const mockOnUploadComplete = jest.fn()
    const user = userEvent.setup()
    
    // Mock successful API response
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        id: 1,
        title: 'test.pdf',
        filename: 'test.pdf',
        status: 'uploaded',
      }),
    })
    
    render(<DocumentUpload onUploadComplete={mockOnUploadComplete} />, { 
      wrapper: createWrapper() 
    })
    
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
    const input = screen.getByRole('button').closest('div')?.querySelector('input[type="file"]')
    
    if (input) {
      await user.upload(input, file)
    }
    
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
    })
    
    const uploadButton = screen.getByText('Upload 1 Files')
    await user.click(uploadButton)
    
    await waitFor(() => {
      expect(mockOnUploadComplete).toHaveBeenCalledWith([
        expect.objectContaining({
          id: 1,
          title: 'test.pdf',
        }),
      ])
    })
  })

  it('handles upload error', async () => {
    const user = userEvent.setup()
    
    // Mock failed API response
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      json: async () => ({
        detail: 'Upload failed',
      }),
    })
    
    render(<DocumentUpload />, { wrapper: createWrapper() })
    
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
    const input = screen.getByRole('button').closest('div')?.querySelector('input[type="file"]')
    
    if (input) {
      await user.upload(input, file)
    }
    
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
    })
    
    const uploadButton = screen.getByText('Upload 1 Files')
    await user.click(uploadButton)
    
    await waitFor(() => {
      expect(screen.getByText('Upload failed')).toBeInTheDocument()
    })
  })

  it('allows removing files before upload', async () => {
    const user = userEvent.setup()
    render(<DocumentUpload />, { wrapper: createWrapper() })
    
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
    const input = screen.getByRole('button').closest('div')?.querySelector('input[type="file"]')
    
    if (input) {
      await user.upload(input, file)
    }
    
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
    })
    
    const removeButton = screen.getByRole('button', { name: '' }) // X button
    await user.click(removeButton)
    
    await waitFor(() => {
      expect(screen.queryByText('test.pdf')).not.toBeInTheDocument()
    })
  })

  it('validates file types', async () => {
    const user = userEvent.setup()
    render(<DocumentUpload />, { wrapper: createWrapper() })
    
    const file = new File(['test content'], 'test.exe', { type: 'application/x-executable' })
    const input = screen.getByRole('button').closest('div')?.querySelector('input[type="file"]')
    
    if (input) {
      // This should trigger the file rejection
      Object.defineProperty(input, 'files', {
        value: [file],
        writable: false,
      })
      fireEvent.change(input)
    }
    
    // The component should show an error for unsupported file type
    // Note: This test might need adjustment based on how react-dropzone handles rejections
  })

  it('shows upload progress', async () => {
    const user = userEvent.setup()
    
    // Mock a slow response to see progress
    ;(global.fetch as jest.Mock).mockImplementationOnce(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          ok: true,
          json: async () => ({ id: 1, title: 'test.pdf' }),
        }), 1000)
      )
    )
    
    render(<DocumentUpload />, { wrapper: createWrapper() })
    
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
    const input = screen.getByRole('button').closest('div')?.querySelector('input[type="file"]')
    
    if (input) {
      await user.upload(input, file)
    }
    
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
    })
    
    const uploadButton = screen.getByText('Upload 1 Files')
    await user.click(uploadButton)
    
    // Should show uploading state
    await waitFor(() => {
      expect(screen.getByText('Uploading...')).toBeInTheDocument()
    })
  })

  it('handles multiple file uploads', async () => {
    const user = userEvent.setup()
    render(<DocumentUpload />, { wrapper: createWrapper() })
    
    const files = [
      new File(['content 1'], 'test1.pdf', { type: 'application/pdf' }),
      new File(['content 2'], 'test2.txt', { type: 'text/plain' }),
    ]
    
    const input = screen.getByRole('button').closest('div')?.querySelector('input[type="file"]')
    
    if (input) {
      await user.upload(input, files)
    }
    
    await waitFor(() => {
      expect(screen.getByText('test1.pdf')).toBeInTheDocument()
      expect(screen.getByText('test2.txt')).toBeInTheDocument()
      expect(screen.getByText('Files (2/10)')).toBeInTheDocument()
    })
  })

  it('clears all files when clear button is clicked', async () => {
    const user = userEvent.setup()
    render(<DocumentUpload />, { wrapper: createWrapper() })
    
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
    const input = screen.getByRole('button').closest('div')?.querySelector('input[type="file"]')
    
    if (input) {
      await user.upload(input, file)
    }
    
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
    })
    
    const clearButton = screen.getByText('Clear All')
    await user.click(clearButton)
    
    await waitFor(() => {
      expect(screen.queryByText('test.pdf')).not.toBeInTheDocument()
    })
  })
})
